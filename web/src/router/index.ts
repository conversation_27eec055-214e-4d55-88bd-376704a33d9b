import { createRouter, createWebHistory } from 'vue-router'


const routes = [
  {
    path: '/',
    name: 'Root',
    redirect: () => {
      // 在重定向时检查认证状态
      const token = localStorage.getItem('token')
      return token ? '/dashboard' : '/login'
    },
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/components/layout/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/dashboard/overview'
      },
      {
        path: 'overview',
        name: 'Overview',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '概览' },

      },
      {
        path: 'documents',
        name: 'DocumentList',
        component: () => import('@/views/DocumentList.vue'),
        meta: { title: '文档列表' },

      },
      {
        path: 'api-keys',
        name: 'ApiKeys',
        component: () => import('@/views/ApiKeys.vue'),
        meta: { title: 'API 密钥' },

      },
      {
        path: 'mineru-nodes',
        name: 'MinerUNodes',
        component: () => import('@/views/MinerUNodes.vue'),
        meta: { title: 'MinerU 节点管理' },

      },
      // {
      //   path: 'parse-tasks',
      //   name: 'ParseTasks',
      //   component: () => import('@/views/ParseTasks.vue'),
      //   meta: { title: '解析任务' },
      // },
      // {
      //   path: 'statistics',
      //   name: 'Statistics',
      //   component: () => import('@/views/Statistics.vue'),
      //   meta: { title: '统计分析' },

      // },
      // {
      //   path: 'logs/system',
      //   name: 'SystemLogs',
      //   component: () => import('@/views/logs/SystemLogs.vue'),
      //   meta: { title: '系统日志' }
      // },
      // {
      //   path: 'logs/parsing',
      //   name: 'ParsingLogs',
      //   component: () => import('@/views/logs/ParsingLogs.vue'),
      //   meta: { title: '解析日志' }
      // },
      // {
      //   path: 'logs/error',
      //   name: 'ErrorLogs',
      //   component: () => import('@/views/logs/ErrorLogs.vue'),
      //   meta: { title: '错误日志' }
      // },
      // {
      //   path: 'logs/access',
      //   name: 'AccessLogs',
      //   component: () => import('@/views/logs/AccessLogs.vue'),
      //   meta: { title: '访问日志' }
      // },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/Settings.vue'),
        meta: { title: '系统设置' },
      },

    ]
  },
  // 处理旧的路径，重定向到新的路径结构
  {
    path: '/documents',
    redirect: '/dashboard/documents'
  },
  {
    path: '/api-keys',
    redirect: '/dashboard/api-keys'
  },
  {
    path: '/statistics',
    redirect: '/dashboard/statistics'
  },
  {
    path: '/settings',
    redirect: '/dashboard/settings'
  },
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/dashboard'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')

  console.log('路由守卫:', { to: to.path, from: from.path, hasToken: !!token })

  if (to.meta.requiresAuth !== false && !token) {
    // 需要认证但没有token，跳转到登录页
    console.log('无token，跳转到登录页')
    next('/login')
  } else if (to.path === '/login' && token) {
    // 已登录用户访问登录页，跳转到首页
    console.log('已登录用户访问登录页，跳转到dashboard')
    next('/dashboard')
  } else {
    console.log('正常路由跳转')
    next()
  }
})

export default router
