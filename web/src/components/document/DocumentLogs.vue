<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'文档日志 - ' + documentId"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
    class="logs-dialog"
  >
    <div class="logs-container">
      <div class="logs-header">
        <span class="logs-count">日志数量: {{ logLines.length }}</span>
        <span class="connection-status" :class="{ connected: wsConnection }">
          {{ wsConnection ? '已连接' : '未连接' }}
        </span>
      </div>
      <div class="logs-wrapper">
        <div class="logs-viewer" ref="logsViewerRef">
          <div
            v-for="(line, index) in logLines"
            :key="index"
            class="log-line"
          >
            <span :class="line.class">{{ line.text }}</span>
          </div>
          <div v-if="logLines.length === 0" class="no-logs">
            暂无日志数据，等待连接...
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createLogWebSocket } from '@/api/document'
import { LogLine } from '@/types/document'

const props = defineProps<{
  visible: boolean
  documentId: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const logLines = ref<LogLine[]>([])
const shouldAutoScroll = ref(true)
const logsViewerRef = ref<HTMLElement | null>(null)
let wsConnection: WebSocket | null = null
let scrollTimer: number | null = null
let scrollStopTimer: number | null = null

// 获取日志类型样式
const getLogClass = (line: string) => {
  const upperLine = line.toUpperCase()
  if (upperLine.includes('ERROR') || upperLine === 'ERROR') return 'log-error'
  if (upperLine.includes('WARNING') || upperLine === 'WARNING' || upperLine === 'WARN') return 'log-warning'
  if (upperLine.includes('DEBUG') || upperLine === 'DEBUG') return 'log-debug'
  if (upperLine.includes('INFO') || upperLine === 'INFO') return 'log-info'
  return 'log-info'  // 默认样式
}

// 启动自动滚动恢复定时器
const startAutoScrollTimer = () => {
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
  scrollTimer = window.setTimeout(() => {
    shouldAutoScroll.value = true
    scrollToBottom()
  }, 10000)
}

// 滚动到底部
const scrollToBottom = () => {
  const viewer = logsViewerRef.value
  if (!viewer) return

  viewer.scrollTop = viewer.scrollHeight
}

// 处理滚动事件（简化版）
const handleScroll = () => {
  // 暂时简化，后续可以添加自动滚动逻辑
}

// 连接WebSocket
const connectWebSocket = (documentId: string) => {
  // 验证documentId
  if (!documentId) {
    ElMessage.error('文档ID为空，无法建立日志连接')
    return
  }

  // 清空之前的日志
  logLines.value = []

  // 关闭之前的连接
  if (wsConnection) {
    wsConnection.close()
    wsConnection = null
  }

  try {
    const ws = createLogWebSocket(documentId)
    wsConnection = ws

    ws.onopen = () => {
      ElMessage.success('日志连接成功')
      logLines.value.push({
        text: 'WebSocket连接已建立，等待数据...',
        class: 'log-info'
      })
      nextTick(() => {
        setTimeout(() => {
          scrollToBottom()
        }, 100)
      })
    }

    ws.onmessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data)

        if (data.type === 'connected') {
          logLines.value.push({
            text: data.message,
            class: 'log-info'
          })
        } else if (data.type === 'history') {
          // 处理历史日志
          data.logs.forEach((log: any) => {
            const logText = `[${log.timestamp}] [${log.level}] ${log.message}${log.source ? ` (${log.source})` : ''}`
            logLines.value.push({
              text: logText,
              class: getLogClass(log.level)
            })
          })
        } else if (data.type === 'log') {
          // 处理实时日志
          const logText = `[${new Date(data.timestamp * 1000).toISOString()}] [${data.level}] ${data.message}${data.source ? ` (${data.source})` : ''}`
          logLines.value.push({
            text: logText,
            class: getLogClass(data.level)
          })
        } else {
          // 其他类型的消息，直接显示
          logLines.value.push({
            text: typeof data === 'string' ? data : JSON.stringify(data),
            class: 'log-info'
          })
        }
      } catch (error) {
        // 如果不是JSON，直接作为文本处理
        logLines.value.push({
          text: event.data,
          class: getLogClass(event.data)
        })
      }

      nextTick(() => {
        if (shouldAutoScroll.value) {
          setTimeout(() => {
            scrollToBottom()
          }, 0)
        }
      })
    }

    ws.onerror = (error: Event) => {
      ElMessage.error('日志连接失败，请检查网络连接')
      logLines.value.push({
        text: 'WebSocket连接发生错误',
        class: 'log-error'
      })
      nextTick(() => scrollToBottom())
      if (wsConnection === ws) {
        wsConnection = null
      }
    }

    ws.onclose = (event) => {
      if (event.code !== 1000) {
        ElMessage.warning('日志连接已断开，请重试')
        logLines.value.push({
          text: `WebSocket连接已断开，状态码: ${event.code}`,
          class: 'log-warning'
        })
      }
      nextTick(() => scrollToBottom())
      if (wsConnection === ws) {
        wsConnection = null
      }
    }
  } catch (error) {
    ElMessage.error('创建日志连接失败，请稍后重试')
    logLines.value.push({
      text: '创建WebSocket连接失败',
      class: 'log-error'
    })
    nextTick(() => scrollToBottom())
  }
}

// 处理关闭
const handleClose = () => {
  if (wsConnection) {
    try {
      wsConnection.close(1000, 'User closed')
    } catch (error) {
      // 静默处理关闭错误
    }
    wsConnection = null
  }
  logLines.value = []
  shouldAutoScroll.value = true
  emit('close')
}

// 监听文档ID变化
watch(() => props.documentId, (newId, oldId) => {
  if (newId && props.visible) {
    connectWebSocket(newId)
  }
})

// 监听可见性变化
watch(() => props.visible, (newVisible, oldVisible) => {
  console.log('🔍 [DEBUG] visible变化:', { oldVisible, newVisible, documentId: props.documentId })
  if (newVisible && props.documentId) {
    connectWebSocket(props.documentId)
  }
})

// 组件卸载前清理
onBeforeUnmount(() => {
  if (wsConnection) {
    try {
      wsConnection.close(1000, 'Component unmounted')
    } catch (error) {
      // 静默处理关闭错误
    }
    wsConnection = null
  }
})
</script>

<style scoped>
.logs-container {
  height: 500px;
  background: #1e1e1e;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.logs-header {
  padding: 8px 16px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.logs-count {
  color: #909399;
}

.connection-status {
  color: #f56c6c;
  font-weight: bold;
}

.connection-status.connected {
  color: #67c23a;
}

.logs-viewer {
  height: 100%;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #d4d4d4;
}

.log-line {
  padding: 2px 0;
  word-break: break-all;
}

.log-error {
  color: #f56c6c;
}

.log-warning {
  color: #e6a23c;
}

.log-info {
  color: #67c23a;
}

.log-debug {
  color: #909399;
}

.logs-wrapper {
  flex: 1;
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.no-logs {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #909399;
  font-style: italic;
}
</style> 