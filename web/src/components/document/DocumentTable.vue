<template>
  <div class="table-container">
    <el-table
      v-loading="loading"
      :data="documents"
      border
      style="width: 100%"
      @sort-change="handleSortChange"
      :max-height="tableMaxHeight"
      :header-cell-style="{
        background: '#f5f7fa',
        color: '#606266',
        fontWeight: 600,
        fontSize: '14px'
      }"
    >
      <el-table-column prop="batch_id" label="批次ID" width="320" fixed="left" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="copy-wrapper">
            <span class="id-text">{{ row.batch_id }}</span>
            <el-button
              type="primary"
              link
              size="small"
              class="copy-btn"
              @click="copyToClipboard(row.batch_id)"
            >
              <el-icon><DocumentCopy /></el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="document_id" label="文档ID" width="320" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="copy-wrapper">
            <span class="id-text">{{ row.document_id }}</span>
            <el-button
              type="primary"
              link
              size="small"
              class="copy-btn"
              @click="copyToClipboard(row.document_id)"
            >
              <el-icon><DocumentCopy /></el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="file_name" label="文件名" width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="filename-cell">
            <el-icon><Document /></el-icon>
            <span>{{ row.file_name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="file_type" label="文件类型" width="120" align="center"/>
      <el-table-column prop="file_size" label="文件大小" width="140" align="right" sortable="custom">
        <template #default="{ row }">
          {{ formatFileSize(row.file_size) }}
        </template>
      </el-table-column>
      <el-table-column prop="is_wecom_push" label="企微推送" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_wecom_push ? 'success' : 'info'">
            {{ row.is_wecom_push ? '已推送' : '未推送' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180" show-overflow-tooltip sortable="custom">
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column prop="updated_at" label="更新时间" width="180" show-overflow-tooltip sortable="custom">
        <template #default="{ row }">
          {{ formatDateTime(row.updated_at) }}
        </template>
      </el-table-column>
      <el-table-column label="任务耗时" width="120" show-overflow-tooltip>
        <template #default="{ row }">
          {{ formatDuration(row.created_at, row.updated_at, row.status) || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="处理节点" width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.node_info && row.node_info.name" class="node-info">
            <el-tag size="small" :type="getNodeStatusType(row.node_info.status || 'unknown')">
              {{ row.node_info.name || '未知节点' }}
            </el-tag>
            <div class="node-details">
              ID:{{ row.node_info.id || 'N/A' }} | {{ row.node_info.service_type || '未知' }}
            </div>
          </div>
          <span v-else-if="row.node_id" class="text-orange-500">
            节点ID: {{ row.node_id }}
          </span>
          <span v-else class="text-gray-400">未分配</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template #default="{ row }">
          <div class="operation-buttons">
            <el-button v-if="canRetry(row)" type="primary" link @click="handleRetry(row)">
              重试
            </el-button>
            <el-button type="primary" link @click="handleViewDetails(row)">
              详情
            </el-button>
            <el-button type="primary" link @click="handleViewLogs(row)">
              日志
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        :hide-on-single-page="false"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @update:current-page="$emit('update:current-page', $event)"
        @update:page-size="$emit('update:page-size', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Document } from '@element-plus/icons-vue'
import { Document as DocumentType } from '@/types/document'
import { formatDateTime, formatFileSize, formatDuration } from '@/utils/format'
import { getStatusType, getStatusText } from '@/utils/status'

const props = defineProps<{
  loading: boolean
  documents: DocumentType[]
  total: number
  currentPage: number
  pageSize: number
}>()

// 窗口高度响应式变量
const windowHeight = ref(window.innerHeight)

// 动态计算表格最大高度
const tableMaxHeight = computed(() => {
  // 基础高度：页面头部(100px) + 筛选组件(80px) + 分页组件(52px) + 边距和间距(60px)
  const baseHeight = 292
  // 可用视窗高度减去基础高度
  const availableHeight = windowHeight.value - baseHeight
  // 每行高度约为 60px，根据数据量计算内容高度
  const rowHeight = 60
  const headerHeight = 60
  const contentHeight = props.documents.length * rowHeight + headerHeight

  // 最小高度300px，最大不超过可用高度的90%
  const minHeight = 300
  const maxHeight = Math.max(availableHeight * 0.9, minHeight)

  // 如果内容高度小于可用高度，使用内容高度；否则使用最大高度
  return Math.min(Math.max(contentHeight, minHeight), maxHeight)
})

// 监听窗口大小变化
const handleResize = () => {
  windowHeight.value = window.innerHeight
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const emit = defineEmits<{
  (e: 'retry', document: DocumentType): void
  (e: 'view-details', document: DocumentType): void
  (e: 'view-logs', document: DocumentType): void
  (e: 'update:current-page', value: number): void
  (e: 'update:page-size', value: number): void
  (e: 'sort-change', data: { prop: string, order: string | null }): void
}>()

const currentPage = computed(() => props.currentPage)
const pageSize = computed(() => props.pageSize)

const canRetry = (document: DocumentType) => {
  return document.status === 'FAILED' || document.status === 'PARSING'
}

const getNodeStatusType = (status: string) => {
  if (!status) return 'info'

  switch (status.toLowerCase()) {
    case 'online':
      return 'success'
    case 'busy':
      return 'warning'
    case 'offline':
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

const handleRetry = (document: DocumentType) => {
  emit('retry', document)
}

const handleViewDetails = (document: DocumentType) => {
  emit('view-details', document)
}

const handleViewLogs = (document: DocumentType) => {
  emit('view-logs', document)
}

const handleSizeChange = (val: number) => {
  emit('update:page-size', val)
}

const handleCurrentChange = (val: number) => {
  emit('update:current-page', val)
}

const handleSortChange = ({ prop, order }: { prop: string, order: string | null }) => {
  emit('sort-change', { prop, order })
}

const copyToClipboard = (text: string) => {
  try {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-9999px'
    textArea.style.top = '0'
    document.body.appendChild(textArea)
    textArea.select()
    textArea.setSelectionRange(0, textArea.value.length)
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('复制成功')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped>
.table-container {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
}

:deep(.el-table__fixed-right) {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

:deep(.el-table__fixed-left) {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.12);
}

:deep(.el-table__header-wrapper) {
  background: #f8fafc;
}

:deep(.el-table th.el-table__cell) {
  background: #f8fafc !important;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-weight: 600;
  font-size: 13px;
  padding: 16px 12px;
}

:deep(.el-table td.el-table__cell) {
  border-bottom: 1px solid #f3f4f6;
  padding: 14px 12px;
  color: #374151;
}

:deep(.el-table__body tr:hover > td) {
  background-color: #f9fafb !important;
}

:deep(.el-table__border-left-patch) {
  background: #f8fafc;
}

:deep(.el-table__border-bottom-patch) {
  background: #f8fafc;
}

.copy-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 0;
}

.id-text {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  color: #4b5563;
  font-size: 13px;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.copy-btn {
  opacity: 0;
  transition: all 0.2s ease;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
}

.copy-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.copy-wrapper:hover .copy-btn {
  opacity: 1;
}

.filename-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #374151;
}

.filename-cell .el-icon {
  color: #6b7280;
  font-size: 16px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 6px;
}

.operation-buttons .el-button {
  padding: 6px 12px;
  font-size: 13px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.operation-buttons .el-button--primary.is-link {
  color: #3b82f6;
}

.operation-buttons .el-button--primary.is-link:hover {
  color: #1d4ed8;
  background: #eff6ff;
}

/* 状态标签样式优化 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  padding: 4px 8px;
  border: none;
}

:deep(.el-tag--success) {
  background: #dcfce7;
  color: #166534;
}

:deep(.el-tag--warning) {
  background: #fef3c7;
  color: #92400e;
}

:deep(.el-tag--danger) {
  background: #fee2e2;
  color: #dc2626;
}

:deep(.el-tag--info) {
  background: #e5e7eb;
  color: #374151;
}

.pagination-container {
  flex-shrink: 0;
  height: 52px;
  padding: 8px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: #fafafa;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.02);
}

/* 分页组件样式优化 */
.pagination-container :deep(.el-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 6px;
}

.pagination-container :deep(.el-pagination .btn-prev),
.pagination-container :deep(.el-pagination .btn-next) {
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  color: #606266;
  transition: all 0.2s;
}

.pagination-container :deep(.el-pagination .btn-prev:hover),
.pagination-container :deep(.el-pagination .btn-next:hover) {
  color: #409eff;
  border-color: #409eff;
}

.pagination-container :deep(.el-pagination .el-pager li) {
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.2s;
}

.pagination-container :deep(.el-pagination .el-pager li:hover) {
  color: #409eff;
  border-color: #409eff;
}

.pagination-container :deep(.el-pagination .el-pager li.is-active) {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

.pagination-container :deep(.el-pagination .el-pagination__total) {
  color: #909399;
  font-weight: 400;
}

.pagination-container :deep(.el-pagination .el-pagination__sizes) {
  margin-right: 16px;
}

.pagination-container :deep(.el-pagination .el-pagination__jump) {
  margin-left: 16px;
  color: #909399;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .id-text {
    font-size: 12px;
    padding: 3px 6px;
  }

  .operation-buttons {
    gap: 4px;
  }

  .operation-buttons .el-button {
    padding: 4px 8px;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .table-container {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table th.el-table__cell) {
    padding: 12px 8px;
    font-size: 12px;
  }

  :deep(.el-table td.el-table__cell) {
    padding: 10px 8px;
  }

  .pagination-container {
    padding: 8px;
    height: auto;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .pagination-container :deep(.el-pagination) {
    justify-content: center;
  }

  .pagination-container :deep(.el-pagination .el-pagination__sizes) {
    margin-right: 8px;
  }

  .pagination-container :deep(.el-pagination .el-pagination__jump) {
    margin-left: 8px;
  }

  .copy-wrapper {
    gap: 4px;
  }

  .id-text {
    font-size: 11px;
    padding: 2px 4px;
  }

  .operation-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .operation-buttons .el-button {
    padding: 3px 6px;
    font-size: 11px;
  }
}

.node-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.node-details {
  font-size: 11px;
  color: #666;
  line-height: 1.2;
}
</style> 