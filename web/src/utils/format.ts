import dayjs from 'dayjs'

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(size: number | undefined): string {
  if (size === undefined) return '-'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(2)} ${units[index]}`
}

/**
 * 格式化日期时间
 * @param dateStr ISO 日期字符串
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(dateStr: string | undefined): string {
  if (!dateStr) return '-'

  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}

/**
 * 格式化任务耗时
 * @param createdAt 创建时间
 * @param updatedAt 更新时间
 * @param status 任务状态
 * @returns 格式化后的耗时字符串
 */
export function formatDuration(createdAt: string | undefined, updatedAt?: string | undefined, status?: string): string {
  try {
    if (!createdAt) return '-'

    const created = new Date(createdAt)
    if (isNaN(created.getTime())) return '-'

    let startTime: Date
    let endTime: Date
    let prefix = ''

    if (status === 'COMPLETED' || status === 'FAILED') {
      // 已完成/失败：显示总处理时间 (updated_at - created_at)
      startTime = created
      if (updatedAt) {
        const updated = new Date(updatedAt)
        endTime = isNaN(updated.getTime()) ? new Date() : updated
      } else {
        endTime = new Date()
      }
      prefix = '耗时 '
    } else if (status === 'PARSING') {
      // 解析中：显示距离最后活动的时间 (当前时间 - updated_at)
      if (updatedAt) {
        const updated = new Date(updatedAt)
        startTime = isNaN(updated.getTime()) ? created : updated
      } else {
        startTime = created
      }
      endTime = new Date()
      prefix = '静默 '
    } else {
      // 其他状态：显示从创建到现在的时间
      startTime = created
      endTime = new Date()
      prefix = '等待 '
    }

    const diffMs = endTime.getTime() - startTime.getTime()

    if (diffMs < 0) return '-'

  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  let timeStr = ''
  if (diffDays > 0) {
    timeStr = `${diffDays}天${diffHours % 24}小时`
  } else if (diffHours > 0) {
    timeStr = `${diffHours}小时${diffMinutes % 60}分钟`
  } else if (diffMinutes > 0) {
    timeStr = `${diffMinutes}分钟${diffSeconds % 60}秒`
  } else {
    timeStr = `${diffSeconds}秒`
  }

  return prefix + timeStr
  } catch (error) {
    console.warn('formatDuration error:', error, { createdAt, updatedAt, status })
    return '-'
  }
}