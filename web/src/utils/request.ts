import axios from "axios";
import type { AxiosInstance, AxiosResponse } from "axios";
import { ElMessage } from "element-plus";

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: "/api/v1", // API 基础URL
  timeout: 15000, // 请求超时时间
  headers: {
    "Content-Type": "application/json",
  },
  // 允许跨域请求携带凭证
  withCredentials: true,
  // 允许重定向时携带认证信息
  maxRedirects: 5,
  // 自定义重定向处理
  beforeRedirect: (options, { headers }) => {
    // 确保重定向请求携带原始请求的认证信息
    if (headers?.authorization) {
      options.headers = {
        ...options.headers,
        Authorization: headers.authorization,
      };
    }
  },
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    config.headers = config.headers || {};

    // 如果有 X-API-Key，只用 Authorization，并且删掉 X-API-Key
    if (config.headers["X-API-Key"]) {
      config.headers["Authorization"] = `Bearer ${config.headers["X-API-Key"]}`;
      delete config.headers["X-API-Key"];
      console.log(`使用 X-API-Key（转为 Authorization）认证: ${config.headers["Authorization"]} -> ${config.url}`);
    } else {
      // 登录和注册接口不需要token
      const isAuthEndpoint = config.url?.includes('/login') || config.url?.includes('/register');

      if (!isAuthEndpoint) {
        // 没有 X-API-Key，则用本地 token 认证
        const token = localStorage.getItem("token");
        if (token) {
          config.headers["Authorization"] = `Bearer ${token}`;
          console.log(`设置 Bearer token: ${token} -> ${config.url}`);
        } else {
          console.warn(`请求 ${config.url} 未找到本地 token`);
        }
      } else {
        console.log(`认证端点 ${config.url}，跳过token设置`);
      }
    }

    // 打印完整请求配置
    console.log("请求配置:", {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      headers: config.headers,
      withCredentials: config.withCredentials,
      maxRedirects: config.maxRedirects,
      data: config.data,
    });

    return config;
  },
  (error) => {
    console.error("请求错误:", error);
    return Promise.reject(error);
  }
);


// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log("API Response:", {
      url: response.config.url,
      status: response.status,
      headers: response.headers,
      data: response.data,
    });
    return response.data;
  },
  (error) => {
    console.error("响应错误:", {
      url: error.config?.url,
      status: error.response?.status,
      headers: error.response?.headers,
      data: error.response?.data,
      message: error.message,
    });

    // 处理认证失败
    if (error.response?.status === 401) {
      console.warn("认证失败，清除本地token");
      localStorage.removeItem("token");

      // 如果不是登录页面，跳转到登录页面
      if (!window.location.pathname.includes('/login')) {
        ElMessage.error("登录已过期，请重新登录");
        window.location.href = '/login';
        return Promise.reject(error);
      }
    }

    const message = error.response?.data?.detail || error.message || "请求失败";
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

export default service;
