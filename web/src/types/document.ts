// 文档状态类型
export type DocumentStatus = 
  | 'UPLOADING'    // 上传中
  | 'UPLOADED'     // 已上传
  | 'PARSING'      // 解析中
  | 'COMPLETED'    // 已完成
  | 'FAILED'       // 失败
  | 'RETRY_PENDING' // 等待重试
  | 'FALLBACK_RETRY' // 降级重试中

// 节点信息接口
export interface NodeInfo {
  id: number
  name: string
  host: string
  port: number
  status: string
  parse_mode: string
  service_type: string
}

// 文档对象接口定义
export interface Document {
  document_id: string
  batch_id: string
  user_id: string
  file_name: string
  file_size: number
  file_type: string
  file_path: string
  status: DocumentStatus
  error_message?: string
  parse_config?: string
  result_data?: any
  created_at: string
  updated_at: string
  completed_at?: string
  node_id?: number
  node_info?: NodeInfo
}

// 日志行接口定义
export interface LogLine {
  text: string
  class: string
}

// 查询参数接口
export interface QueryParams {
  page: number
  page_size: number
  document_id?: string
  batch_id?: string
  status?: string
  file_name?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 状态选项接口
export interface StatusOption {
  value: string
  label: string
  type: string
}

// 处理阶段选项接口
export interface StageOption {
  value: string
  label: string
  type: string
}

// 分页信息接口
export interface Pagination {
  total: number
  page: number
  page_size: number
  pages: number
}

// 查询结果接口
export interface QueryResult {
  success: boolean
  message: string
  code: number
  items: Document[]
  pagination: Pagination
}

// 文档解析结果接口
export interface DocumentParseResult {
  document_id: string
  status: string
  result: {
    markdown_text: string
  }
  completed_at: string
}

// 批量上传响应
export interface BatchTaskResponse {
  success: boolean
  message: string
  batch_id: string
  task_count: number
  uploaded_files: Array<{
    document_id: string
    filename: string
    status: string
  }>
  failed_files?: Array<{
    filename: string
    error: string
  }>
}

// 批次状态
export interface BatchStatus {
  batch_id: string
  total_count: number
  completed_count: number
  failed_count: number
  in_progress_count: number
  status: string
  created_at: string
  updated_at: string
} 