<template>
  <div class="document-list">
    <div class="page-header">
      <h2 class="page-title">文档管理</h2>
      <DocumentUpload @upload-success="handleUploadSuccess" />
    </div>

    <div class="content-container">
      <div class="filter-card">
        <DocumentFilter 
          :query-params="queryParams"
          @search="handleSearch"
          @reset="handleReset"
        />
      </div>

      <div class="table-card">
        <DocumentTable
          :loading="loading"
          :documents="documentList"
          :total="total"
          :current-page="queryParams.page"
          :page-size="queryParams.page_size"
          @retry="handleRetry"
          @view-details="handleViewDetails"
          @view-logs="handleViewLogs"
          @update:current-page="handleCurrentChange"
          @update:page-size="handleSizeChange"
          @sort-change="handleSortChange"
        />
      </div>
    </div>

    <DocumentDetails
      v-model:visible="detailsVisible"
      :document="currentDocument"
    />

    <DocumentLogs
      v-model:visible="logsVisible"
      :document-id="currentLogDocumentId"
      @close="handleCloseLog"
    />

    <LoginDialog v-model:visible="loginVisible" />
    <RegisterDialog v-model:visible="registerVisible" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { queryDocuments, retryDocument } from '@/api/document'
import { Document, QueryParams } from '@/types/document'
import { isProcessing } from '@/utils/status'
import DocumentFilter from '@/components/document/DocumentFilter.vue'
import DocumentTable from '@/components/document/DocumentTable.vue'
import DocumentDetails from '@/components/document/DocumentDetails.vue'
import DocumentLogs from '@/components/document/DocumentLogs.vue'
import DocumentUpload from '@/components/document/DocumentUpload.vue'
import LoginDialog from '@/components/LoginDialog.vue'
import RegisterDialog from '@/components/RegisterDialog.vue'

// 数据状态
const loading = ref(false)
const documentList = ref<Document[]>([])
const total = ref(0)
const detailsVisible = ref(false)
const currentDocument = ref<Document | null>(null)
const logsVisible = ref(false)
const currentLogDocumentId = ref('')
const loginVisible = ref(false)
const registerVisible = ref(false)

// 查询参数
const queryParams = ref<QueryParams>({
  page: 1,
  page_size: 10,
  document_id: '',
  batch_id: '',
  status: '',
  file_name: '',
  sort_by: '',
  sort_order: 'desc'
})

// 定时器引用
const queryTimer = ref<number | null>(null)

// 获取文档列表
const getDocumentList = async () => {
  loading.value = true
  try {
    const response = await queryDocuments(queryParams.value)
    console.log('📊 API响应数据:', response)

    // 正确解构 API 返回的数据结构
    const { items, pagination } = response
    documentList.value = items || []
    total.value = pagination?.total || 0

    console.log('📋 文档列表数据:', {
      items: items?.length || 0,
      total: pagination?.total || 0,
      currentPage: queryParams.value.page,
      pageSize: queryParams.value.page_size,
      pagination: pagination
    })

    // 如果有正在处理的文档，启动定时查询
    if (needAutoRefresh(documentList.value)) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  } catch (error) {
    console.error('获取文档列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 判断是否需要自动刷新
const needAutoRefresh = (list: Document[]) => {
  return list.some(doc => isProcessing(doc.status))
}

// 启动定时查询
const startAutoRefresh = () => {
  if (!queryTimer.value) {
    queryTimer.value = window.setInterval(() => {
      getDocumentList()
    }, 10000)
  }
}

// 停止定时查询
const stopAutoRefresh = () => {
  if (queryTimer.value) {
    window.clearInterval(queryTimer.value)
    queryTimer.value = null
  }
}

// 重试处理
const handleRetry = async (document: Document) => {
  const loadingInstance = ElLoading.service({
    target: '.document-list',
    text: '提交重试请求...'
  })
  try {
    const isProcessing = document.status === 'PARSING'
    const confirmMessage = isProcessing
      ? '该文档正在解析中，确认要强制重试吗？这将中断当前处理。'
      : '确认要重试处理该文档吗？'

    await ElMessageBox.confirm(confirmMessage, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 如果是解析中的任务，使用强制重试
    await retryDocument(document.document_id, isProcessing)
    ElMessage.success('重试任务已提交')
    getDocumentList()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('重试失败: ' + error.message)
    }
  } finally {
    loadingInstance.close()
  }
}

// 查看详情
const handleViewDetails = (document: Document) => {
  currentDocument.value = document
  detailsVisible.value = true
}

// 查看日志
const handleViewLogs = (document: Document) => {
  console.log('🔍 [DEBUG] handleViewLogs 被调用')
  console.log('🔍 [DEBUG] document:', document)
  console.log('🔍 [DEBUG] document.document_id:', document.document_id)

  currentLogDocumentId.value = document.document_id
  logsVisible.value = true

  console.log('🔍 [DEBUG] 设置后的值:')
  console.log('🔍 [DEBUG] currentLogDocumentId.value:', currentLogDocumentId.value)
  console.log('🔍 [DEBUG] logsVisible.value:', logsVisible.value)
}

// 关闭日志对话框
const handleCloseLog = () => {
  logsVisible.value = false
}

// 搜索处理
const handleSearch = () => {
  queryParams.value.page = 1
  getDocumentList()
}

// 重置查询
const handleReset = () => {
  queryParams.value = {
    page: 1,
    page_size: 10,
    document_id: '',
    batch_id: '',
    status: '',
    file_name: '',
    sort_by: '',
    sort_order: 'desc'
  }
  handleSearch()
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  queryParams.value.page_size = val
  getDocumentList()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  queryParams.value.page = val
  getDocumentList()
}

// 排序改变
const handleSortChange = ({ prop, order }: { prop: string, order: string | null }) => {
  queryParams.value.sort_by = order ? prop : ''
  queryParams.value.sort_order = order === 'ascending' ? 'asc' : 'desc'
  getDocumentList()
}

// 上传成功处理
const handleUploadSuccess = () => {
  getDocumentList()
}

// 组件卸载前清理
onBeforeUnmount(() => {
  stopAutoRefresh()
})

// 初始化
onMounted(() => {
  getDocumentList()
})
</script>

<style scoped>
.document-list {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.page-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-card {
  flex-shrink: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.table-card {
  background: transparent;
  display: flex;
  flex-direction: column;
}

/* 响应式布局 */
@media (max-width: 1024px) {
  .document-list {
    padding: 16px;
    gap: 12px;
  }

  .page-header {
    padding: 16px 20px;
  }

  .page-title {
    font-size: 22px;
  }
}

@media (max-width: 768px) {
  .document-list {
    padding: 12px;
    gap: 8px;
    background: #f8fafc;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    border-radius: 8px;
  }

  .page-title {
    font-size: 20px;
  }

  .content-container {
    gap: 8px;
  }

  .filter-card {
    border-radius: 8px;
  }
}
</style>
