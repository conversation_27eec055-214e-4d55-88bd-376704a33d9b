import request from '@/utils/request'
import type { Document, QueryParams, QueryResult, DocumentParseResult } from '@/types/document'

export interface QueryResponse {
  items: Document[]
  total: number
}

/**
 * 查询文档列表
 */
export const queryDocuments = (params: QueryParams): Promise<QueryResult> => {
  return request({
    url: '/document/documents',
    method: 'get',
    params
  })
}

/**
 * 重试处理文档
 * @param documentId 文档ID
 * @param force 是否强制重试
 */
export const retryDocument = (documentId: string, force: boolean = false): Promise<void> => {
  return request({
    url: `/document/documents/${documentId}/retry`,
    method: 'post',
    data: {
      force: force
    }
  })
}

/**
 * 获取文档解析结果
 * @param documentId 文档ID
 */
export const getDocumentResult = (documentId: string): Promise<DocumentParseResult> => {
  return request({
    url: `/document/${documentId}/result`,
    method: 'get',
    params: { content_type: 'json' }
  })
}

/**
 * 创建WebSocket连接获取文档日志
 * @param documentId 文档ID
 * @returns WebSocket实例
 */
export function createLogWebSocket(documentId: string): WebSocket {
  console.log('🔍 [DEBUG] createLogWebSocket 被调用，documentId:', documentId)

  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'

  // 修复：根据环境确定正确的后端主机地址
  let backendHost: string

  if (import.meta.env.DEV) {
    // 开发环境：使用固定的后端端口
    backendHost = 'localhost:8010'
    console.log('🔍 [DEBUG] 开发环境，使用固定后端地址')
  } else {
    // 生产环境：使用当前域名但可能需要不同端口
    const currentHost = window.location.hostname
    backendHost = `${currentHost}:8010`  // 或者根据实际部署情况调整
    console.log('🔍 [DEBUG] 生产环境，使用当前域名')
  }

  console.log('🔍 [DEBUG] Protocol:', protocol)
  console.log('🔍 [DEBUG] Backend Host:', backendHost)
  console.log('🔍 [DEBUG] window.location.host:', window.location.host)
  console.log('🔍 [DEBUG] window.location.protocol:', window.location.protocol)

  // 获取认证token
  const token = localStorage.getItem('token')
  console.log('🔍 [DEBUG] Token获取结果:', token ? `存在(长度:${token.length})` : '不存在')

  const wsUrl = `${protocol}//${backendHost}/api/v1/manage/ws/logs/document/${documentId}${token ? `?token=${token}` : ''}`

  console.log('🌐 [DEBUG] 完整WebSocket URL:', wsUrl)

  try {
    const ws = new WebSocket(wsUrl)
    console.log('✅ [DEBUG] WebSocket对象创建成功')
    return ws
  } catch (error) {
    console.error('❌ [ERROR] WebSocket创建失败:', error)
    throw error
  }
}

// 上传文档
export function uploadDocuments(data: FormData, apiKey: string) {
  const headers: Record<string, string> = {
    'Content-Type': 'multipart/form-data',
    'X-API-Key': apiKey
  }

  return request({
    url: '/document/upload',
    method: 'post',
    data,
    headers
  })
}

// 获取文档日志
export function getDocumentLogs(documentId: string, params: any) {
  return request({
    url: `/document/documents/${documentId}/logs`,
    method: 'get',
    params
  })
}

// 获取文档详情
export function getDocument(documentId: string) {
  return request({
    url: `/document/documents/${documentId}`,
    method: 'get'
  })
}

// 获取批次状态
export function getBatchStatus(batchId: string) {
  return request({
    url: `/document/batch/${batchId}`,
    method: 'get'
  })
} 