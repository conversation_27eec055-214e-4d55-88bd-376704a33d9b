#!/usr/bin/env python3
"""
测试路由是否正确配置，没有重复
"""
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from mineru_api.main import app


def test_routes():
    """测试路由配置"""
    print("🧪 测试API路由配置")
    print("=" * 50)
    
    # 获取所有路由
    routes = []
    for route in app.app.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            for method in route.methods:
                if method != 'HEAD':  # 忽略HEAD方法
                    routes.append(f"{method} {route.path}")
    
    # 按路径排序
    routes.sort()
    
    print("📋 注册的路由:")
    for route in routes:
        print(f"  {route}")
    
    print(f"\n📊 总计: {len(routes)} 个路由")
    
    # 检查是否有重复路由
    duplicates = []
    seen = set()
    for route in routes:
        if route in seen:
            duplicates.append(route)
        else:
            seen.add(route)
    
    if duplicates:
        print(f"\n❌ 发现重复路由:")
        for dup in duplicates:
            print(f"  {dup}")
    else:
        print(f"\n✅ 没有发现重复路由")
    
    # 检查关键路由是否存在
    key_routes = [
        "POST /parse/upload",
        "GET /tasks/{task_id}/status",  # 新的任务状态路由
        "GET /sglang/status",           # 新的sglang状态路由
        "POST /sglang/start",           # 新的sglang管理路由
        "GET /health",                  # 健康检查
    ]
    
    print(f"\n🔍 检查关键路由:")
    for key_route in key_routes:
        if key_route in routes:
            print(f"  ✅ {key_route}")
        else:
            print(f"  ❌ {key_route} (缺失)")


if __name__ == "__main__":
    test_routes()
