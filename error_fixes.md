# 前端错误修复

## 问题分析

从浏览器控制台截图中发现有JavaScript错误，可能与新添加的功能相关。

## 潜在错误源

1. **formatDuration函数**：
   - 可能接收到undefined或无效的日期字符串
   - Date构造函数可能返回Invalid Date
   - 时间计算可能产生负值或NaN

2. **节点信息访问**：
   - node_info对象可能为null但仍尝试访问其属性
   - node_info的子属性可能不存在
   - getNodeStatusType函数可能接收到undefined状态

## 修复内容

### 1. 增强formatDuration函数的错误处理

**文件**: `web/src/utils/format.ts`

```typescript
export function formatDuration(createdAt: string | undefined, updatedAt?: string | undefined, status?: string): string {
  try {
    if (!createdAt) return '-'

    const created = new Date(createdAt)
    if (isNaN(created.getTime())) return '-'  // 检查无效日期

    let startTime: Date
    let endTime: Date
    let prefix = ''

    if (status === 'COMPLETED' || status === 'FAILED') {
      startTime = created
      if (updatedAt) {
        const updated = new Date(updatedAt)
        endTime = isNaN(updated.getTime()) ? new Date() : updated  // 安全检查
      } else {
        endTime = new Date()
      }
      prefix = '耗时 '
    } else if (status === 'PARSING') {
      if (updatedAt) {
        const updated = new Date(updatedAt)
        startTime = isNaN(updated.getTime()) ? created : updated  // 安全检查
      } else {
        startTime = created
      }
      endTime = new Date()
      prefix = '静默 '
    } else {
      startTime = created
      endTime = new Date()
      prefix = '等待 '
    }

    const diffMs = endTime.getTime() - startTime.getTime()
    if (diffMs < 0) return '-'

    // ... 时间计算逻辑 ...
    
    return prefix + timeStr
  } catch (error) {
    console.warn('formatDuration error:', error, { createdAt, updatedAt, status })
    return '-'
  }
}
```

### 2. 增强节点信息显示的安全性

**文件**: `web/src/components/document/DocumentTable.vue`

#### 2.1 任务耗时列
```vue
<el-table-column label="任务耗时" width="120" show-overflow-tooltip>
  <template #default="{ row }">
    {{ formatDuration(row.created_at, row.updated_at, row.status) || '-' }}
  </template>
</el-table-column>
```

#### 2.2 处理节点列
```vue
<el-table-column label="处理节点" width="150" show-overflow-tooltip>
  <template #default="{ row }">
    <div v-if="row.node_info && row.node_info.name" class="node-info">
      <el-tag size="small" :type="getNodeStatusType(row.node_info.status || 'unknown')">
        {{ row.node_info.name || '未知节点' }}
      </el-tag>
      <div class="node-details">
        ID:{{ row.node_info.id || 'N/A' }} | {{ row.node_info.service_type || '未知' }}
      </div>
    </div>
    <span v-else-if="row.node_id" class="text-orange-500">
      节点ID: {{ row.node_id }}
    </span>
    <span v-else class="text-gray-400">未分配</span>
  </template>
</el-table-column>
```

#### 2.3 节点状态类型函数
```typescript
const getNodeStatusType = (status: string) => {
  if (!status) return 'info'
  
  switch (status.toLowerCase()) {
    case 'online':
      return 'success'
    case 'busy':
      return 'warning'
    case 'offline':
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}
```

## 安全检查要点

### 1. 空值检查
- 检查对象是否存在再访问属性
- 使用 `||` 操作符提供默认值
- 使用可选链操作符 `?.` (如果支持)

### 2. 日期处理
- 使用 `isNaN(date.getTime())` 检查无效日期
- 为无效日期提供回退值
- 处理时间差为负数的情况

### 3. 函数参数
- 检查参数是否为undefined或null
- 为可选参数提供默认值
- 使用类型守卫确保参数类型正确

### 4. 错误边界
- 使用try-catch包装可能出错的代码
- 提供有意义的错误日志
- 确保错误不会导致整个组件崩溃

## 测试建议

1. **数据边界测试**：
   - 测试空数据、null数据的情况
   - 测试无效日期字符串
   - 测试缺少字段的文档对象

2. **状态测试**：
   - 测试各种文档状态的耗时显示
   - 测试不同节点状态的显示效果
   - 测试节点信息缺失的情况

3. **错误恢复测试**：
   - 故意传入错误数据，验证是否优雅降级
   - 检查控制台是否有未捕获的错误
   - 验证用户界面是否保持可用

## 预防措施

1. **类型安全**：
   - 使用TypeScript的严格模式
   - 为所有接口定义完整的类型
   - 使用可选属性标记可能不存在的字段

2. **数据验证**：
   - 在API响应处理时验证数据结构
   - 使用数据转换函数确保数据格式正确
   - 为缺失的字段提供默认值

3. **错误监控**：
   - 添加全局错误处理器
   - 使用错误边界组件捕获组件级错误
   - 记录错误信息用于调试

这些修复确保了前端代码在面对不完整或无效数据时能够优雅地处理，避免JavaScript错误导致页面崩溃。
