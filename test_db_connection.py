#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
数据库连接测试脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from almond_parser.config import settings
from almond_parser.db import init_database, get_db
from almond_parser.db.models import User
from almond_parser.utils.auth import get_password_hash
from sqlalchemy import select, text
from loguru import logger


async def test_database():
    """测试数据库连接和基础功能"""
    try:
        logger.info("🔍 开始测试数据库连接...")
        
        # 测试数据库连接
        logger.info(f"📊 数据库配置:")
        logger.info(f"  - Host: {settings.MYSQL_HOST}:{settings.MYSQL_PORT}")
        logger.info(f"  - Database: {settings.MYSQL_DATABASE}")
        logger.info(f"  - User: {settings.MYSQL_USER}")
        logger.info(f"  - URL: {settings.database_url}")
        
        # 初始化数据库
        await init_database()
        logger.info("✅ 数据库连接成功")
        
        # 测试基础查询
        async with get_db() as db:
            # 测试简单查询
            result = await db.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            logger.info(f"✅ 基础查询测试成功: {test_value}")
            
            # 检查用户表
            result = await db.execute(select(User))
            users = result.scalars().all()
            logger.info(f"📊 用户表中有 {len(users)} 个用户")
            
            if len(users) == 0:
                logger.warning("⚠️  用户表为空，创建默认管理员用户...")
                
                # 创建默认管理员用户
                admin_user = User(
                    username="admin",
                    password=get_password_hash("admin123"),
                    is_admin=True,
                    is_active=True
                )
                
                db.add(admin_user)
                await db.commit()
                await db.refresh(admin_user)
                
                logger.info(f"✅ 创建默认管理员用户成功: admin / admin123")
            else:
                for user in users:
                    logger.info(f"  - 用户: {user.username}, ID: {user.id}, 管理员: {user.is_admin}, 激活: {user.is_active}")
        
        logger.info("🎉 数据库测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_redis():
    """测试Redis连接"""
    try:
        logger.info("🔍 开始测试Redis连接...")
        
        from almond_parser.db.redis_client import get_redis
        
        redis = await get_redis()
        
        # 测试基础操作
        await redis.set("test_key", "test_value", ex=10)
        value = await redis.get("test_key")
        
        if value == "test_value":
            logger.info("✅ Redis连接测试成功")
            return True
        else:
            logger.error("❌ Redis数据不一致")
            return False
            
    except Exception as e:
        logger.error(f"❌ Redis连接测试失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("🚀 开始系统连接测试...")
    
    db_ok = await test_database()
    redis_ok = await test_redis()
    
    if db_ok and redis_ok:
        logger.info("🎉 所有连接测试通过，系统可以正常启动")
        return True
    else:
        logger.error("❌ 连接测试失败，请检查配置")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
