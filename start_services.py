#!/usr/bin/env python3
"""
AI中心解析流 - 服务启动脚本

根据安装的环境自动启动对应的服务
"""

import argparse
import os
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional


def read_env_info() -> Dict[str, str]:
    """读取环境信息"""
    env_info = {}
    env_file = Path(".env_info")

    if env_file.exists():
        with open(env_file, "r") as f:
            for line in f:
                if "=" in line:
                    key, value = line.strip().split("=", 1)
                    env_info[key] = value

    return env_info


def check_service_available(service: str) -> bool:
    """检查服务是否可用"""
    service_paths = {
        "mineru-api": "mineru_api/main.py",
        "almond-parser": "almond_parser/main.py"
    }

    path = service_paths.get(service)
    if path:
        return Path(path).exists()
    return False


def start_mineru_api(port: int = 8000, host: str = "0.0.0.0") -> subprocess.Popen:
    """启动 MinerU API 服务"""
    print(f"🚀 启动 MinerU API 服务 (http://{host}:{port})")

    cmd = [
        "uv", "run", "mineru_api/main.py",
        "--host", host,
        "--port", str(port)
    ]

    return subprocess.Popen(
        cmd,
        cwd=Path.cwd(),
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        bufsize=1,
        universal_newlines=True
    )


def start_almond_parser(port: int = 8001, host: str = "0.0.0.0") -> subprocess.Popen:
    """启动杏仁解析服务"""
    print(f"🚀 启动杏仁解析服务 (http://{host}:{port})")

    cmd = [
        "uv", "run", "python", "almond_parser/main.py",
        "--host", host,
        "--port", str(port)
    ]

    return subprocess.Popen(
        cmd,
        cwd=Path.cwd(),
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        bufsize=1,
        universal_newlines=True
    )


def monitor_processes(processes: List[subprocess.Popen], names: List[str]):
    """监控进程状态"""
    print("\n📊 服务状态监控 (Ctrl+C 停止所有服务)")
    print("=" * 50)

    try:
        while True:
            all_running = True
            for i, (process, name) in enumerate(zip(processes, names)):
                if process.poll() is None:
                    print(f"✅ {name}: 运行中")
                else:
                    print(f"❌ {name}: 已停止 (退出码: {process.returncode})")
                    all_running = False

            if not all_running:
                print("\n⚠️  有服务停止运行，正在停止所有服务...")
                break

            time.sleep(5)
            print("\n" + "=" * 50)

    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭所有服务...")

    # 停止所有进程
    for process, name in zip(processes, names):
        if process.poll() is None:
            print(f"🔄 停止 {name}...")
            process.terminate()
            try:
                process.wait(timeout=10)
                print(f"✅ {name} 已停止")
            except subprocess.TimeoutExpired:
                print(f"⚠️  {name} 强制终止")
                process.kill()


def main():
    parser = argparse.ArgumentParser(
        description="AI中心解析流服务启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start_services.py                    # 根据环境自动启动
  python start_services.py --service mineru-api  # 仅启动 MinerU API
  python start_services.py --all              # 启动所有可用服务
        """
    )

    parser.add_argument(
        "--service",
        choices=["mineru-api", "almond-parser"],
        help="指定启动的服务"
    )

    parser.add_argument(
        "--all",
        action="store_true",
        help="启动所有可用服务"
    )

    parser.add_argument(
        "--mineru-port",
        type=int,
        default=8000,
        help="MinerU API 端口 (默认: 8000)"
    )

    parser.add_argument(
        "--almond-port",
        type=int,
        default=8001,
        help="杏仁解析服务端口 (默认: 8001)"
    )

    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="绑定主机 (默认: 0.0.0.0)"
    )

    args = parser.parse_args()

    print("🎯 AI中心解析流服务启动器")
    print("=" * 50)

    # 读取环境信息
    env_info = read_env_info()
    installed_service = env_info.get("service", "unknown")

    print(f"📝 检测到安装环境: {installed_service}")

    # 确定要启动的服务
    services_to_start = []

    if args.service:
        # 指定服务
        if check_service_available(args.service):
            services_to_start.append(args.service)
        else:
            print(f"❌ 服务 {args.service} 不可用")
            sys.exit(1)

    elif args.all:
        # 启动所有可用服务
        for service in ["mineru-api", "almond-parser"]:
            if check_service_available(service):
                services_to_start.append(service)

    else:
        # 根据环境自动启动
        if installed_service == "all":
            for service in ["mineru-api", "almond-parser"]:
                if check_service_available(service):
                    services_to_start.append(service)
        elif installed_service in ["mineru-api", "almond-parser"]:
            if check_service_available(installed_service):
                services_to_start.append(installed_service)

    if not services_to_start:
        print("❌ 没有可启动的服务")
        sys.exit(1)

    print(f"🚀 准备启动服务: {', '.join(services_to_start)}")

    # 启动服务
    processes = []
    names = []

    for service in services_to_start:
        try:
            if service == "mineru-api":
                process = start_mineru_api(args.mineru_port, args.host)
                processes.append(process)
                names.append("MinerU API")

            elif service == "almond-parser":
                process = start_almond_parser(args.almond_port, args.host)
                processes.append(process)
                names.append("杏仁解析服务")

        except Exception as e:
            print(f"❌ 启动 {service} 失败: {e}")
            # 停止已启动的进程
            for p in processes:
                if p.poll() is None:
                    p.terminate()
            sys.exit(1)

    if processes:
        # 等待服务启动
        time.sleep(2)

        # 检查服务是否成功启动
        failed_services = []
        for process, name in zip(processes, names):
            if process.poll() is not None:
                failed_services.append(name)

        if failed_services:
            print(f"❌ 以下服务启动失败: {', '.join(failed_services)}")
            sys.exit(1)

        print(f"✅ 所有服务启动成功!")

        # 监控进程
        monitor_processes(processes, names)

    print("\n👋 所有服务已停止")


if __name__ == "__main__":
    main()
