# almond_parser 任务状态同步增强

## 🎯 解决的问题

### 核心问题
即使 mineru-api 已经增强了回调可靠性，但如果回调彻底失败，almond_parser 仍然会永远等待，任务卡在"解析中"状态。

### 解决方案
添加一个**最小侵入性的任务状态同步模块**，定期检查长时间未更新的任务，主动从 mineru-api 查询状态并同步结果。

## 🛠️ 新增组件

### 1. 任务状态同步服务 (`task_sync_service.py`)

#### 核心功能
- **定期扫描**：每10分钟检查一次长时间未更新的任务
- **主动查询**：调用 mineru-api 的状态查询接口
- **状态同步**：根据查询结果更新本地任务状态
- **结果获取**：对于已完成的任务，主动获取解析结果

#### 工作流程
```
1. 扫描数据库 → 查找超过15分钟未更新且状态为"解析中"的任务
2. 逐个查询 → 调用 mineru-api 的 /tasks/{task_id}/status 接口
3. 状态同步 → 根据返回结果更新本地状态：
   - completed → 获取结果并更新为已完成
   - failed → 更新为失败状态
   - not_found → 标记为失败（任务已被清理）
   - processing → 更新时间戳（任务仍在进行）
4. 记录日志 → 详细记录同步过程和结果
```

#### 配置参数
```python
sync_interval_minutes = 10           # 同步间隔（分钟）
stuck_task_threshold_minutes = 15    # 卡住任务阈值（分钟）
max_sync_tasks_per_run = 20         # 每次最多同步任务数
```

### 2. 定时任务集成 (`arq_app.py`)

#### 新增定时任务
```python
# 每10分钟同步卡住的任务状态
cron(
    coroutine=task_sync_service.sync_stuck_tasks,
    minute={0, 10, 20, 30, 40, 50},
    run_at_startup=False,
)
```

#### 与现有定时任务的关系
- **节点健康检查**：每30秒检查节点状态
- **重试任务处理**：每5分钟处理重试任务
- **过期记录清理**：每小时清理过期记录
- **任务状态同步**：每10分钟同步卡住的任务（新增）

## 📋 使用方法

### 1. 自动运行
服务启动后，任务状态同步会自动运行，无需手动干预：

```bash
# 启动 ARQ Worker（包含定时任务）
python almond_parser/worker.py

# 或者使用统一启动脚本
python almond_parser/start_services.py
```

### 2. 手动触发
如果需要立即同步，可以手动调用：

```python
from almond_parser.tasks.task_sync_service import task_sync_service

# 手动同步一次
result = await task_sync_service.sync_stuck_tasks()
print(f"同步结果: {result}")
```

### 3. 监控和日志
同步过程会产生详细的日志：

```bash
# 查看同步日志
tail -f logs/almond_parser.log | grep "task_sync"

# 查看文档处理日志
tail -f logs/almond_parser.log | grep "sync_stuck_tasks"
```

## 🔧 配置选项

### 环境变量（可选）
```bash
# 如果需要自定义同步参数，可以在代码中修改
# 或者通过配置文件设置
```

### 数据库配置
无需额外配置，使用现有的数据库连接。

## 📊 监控指标

### 同步统计
每次同步会返回统计信息：
```json
{
    "checked_tasks": 5,      // 检查的任务数
    "synced_tasks": 3,       // 成功同步的任务数
    "completed_tasks": 2,    // 同步到完成状态的任务数
    "failed_tasks": 1,       // 同步到失败状态的任务数
    "errors": []             // 错误信息列表
}
```

### 日志记录
- **INFO**：正常的同步操作
- **WARNING**：任务在 mineru-api 中不存在等异常情况
- **ERROR**：同步过程中的错误

## 🔄 工作场景

### 场景1：回调失败但任务已完成
```
1. mineru-api 完成了 OCR 处理
2. 回调发送失败（网络问题、almond_parser 临时不可用等）
3. almond_parser 中任务仍显示"解析中"
4. 定时同步检测到这个任务
5. 查询 mineru-api 发现任务已完成
6. 获取结果并更新本地状态为"已完成"
```

### 场景2：任务在 mineru-api 中失败
```
1. mineru-api 处理过程中出错
2. 回调发送失败
3. almond_parser 中任务仍显示"解析中"
4. 定时同步检测到这个任务
5. 查询 mineru-api 发现任务已失败
6. 更新本地状态为"失败"
```

### 场景3：任务被清理
```
1. mineru-api 中的任务因为过期被清理
2. almond_parser 中任务仍显示"解析中"
3. 定时同步检测到这个任务
4. 查询 mineru-api 返回"任务不存在"
5. 标记本地任务为失败
```

## 🧪 测试验证

### 运行测试
```bash
cd almond_parser
python tests/test_task_sync.py
```

### 测试内容
- 查找卡住的任务功能
- 同步服务基本功能
- 定时任务配置验证
- 数据库操作测试

## 📁 文件结构

```
almond_parser/
├── tasks/
│   ├── task_sync_service.py     # 任务状态同步服务（新增）
│   └── arq_app.py              # ARQ配置（已修改，新增定时任务）
├── tests/
│   └── test_task_sync.py       # 测试脚本（新增）
└── docs/
    └── task_sync_enhancement.md # 本文档（新增）
```

## ✅ 优势特点

1. **最小侵入性** - 不修改现有核心业务逻辑
2. **自动恢复** - 定期自动检测和修复卡住的任务
3. **完整性保障** - 确保任务状态的最终一致性
4. **可观测性** - 详细的日志和统计信息
5. **性能友好** - 限制每次处理的任务数量，避免系统负载过高
6. **容错性强** - 即使 mineru-api 不可用，也不会影响正常业务

## 🚀 部署建议

### 1. 渐进式启用
- 先在测试环境验证功能
- 观察同步效果和系统负载
- 逐步在生产环境启用

### 2. 监控要点
- 同步任务的执行频率和耗时
- 卡住任务的数量趋势
- 同步成功率和错误率

### 3. 调优建议
- 根据实际情况调整同步间隔和阈值
- 监控数据库查询性能
- 根据 mineru-api 的负载调整并发数

## 🔮 后续优化

### 可选增强
1. **智能重试** - 对同步失败的任务实施智能重试策略
2. **批量查询** - 支持批量查询多个任务状态，提高效率
3. **状态预测** - 基于历史数据预测任务完成时间
4. **告警机制** - 当卡住任务数量异常时发送告警

### 与 mineru-api 的协同
- 利用 mineru-api 的新增统计接口
- 支持更细粒度的状态查询
- 实现双向状态校验机制
