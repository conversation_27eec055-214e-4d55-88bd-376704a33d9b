# ARQ 定时任务修复说明

## 问题描述

Worker 启动后定时任务没有执行，日志显示错误：

```
RuntimeError: '*/60'
```

## 问题原因

**ARQ cron 语法错误**：

1. **不支持 `second` 参数**：
   - ARQ 的 cron 不支持秒级间隔
   - 最小时间单位是 `minute`（分钟）

2. **错误的配置**：
   ```python
   # ❌ 错误：ARQ 不支持 second 参数
   cron(
       coroutine=node_tasks.health_check_all_nodes,
       second=settings.NODE_HEALTH_CHECK_INTERVAL,  # 60秒
       run_at_startup=True,
   )
   ```

3. **ARQ 内部转换错误**：
   - ARQ 将 `second=60` 转换为 `minute='*/60'`
   - 但 `*/60` 是无效的 cron 表达式（分钟只能是 0-59）

## 修复方案

### 修改前（错误）

```python
cron_jobs = [
    cron(
        coroutine=node_tasks.health_check_all_nodes,
        second=settings.NODE_HEALTH_CHECK_INTERVAL,  # ❌ 不支持
        run_at_startup=True,
    )
]
```

### 修改后（正确）

```python
cron_jobs = [
    cron(
        coroutine=node_tasks.health_check_all_nodes,
        minute=1,  # ✅ 每分钟执行一次
        run_at_startup=True,
    )
]
```

## ARQ Cron 支持的时间单位

| 参数 | 说明 | 示例 |
|------|------|------|
| `minute` | 分钟 (0-59) | `minute=1` (每分钟), `minute=30` (每30分钟) |
| `hour` | 小时 (0-23) | `hour=1` (每小时), `hour=6` (每6小时) |
| `day` | 日期 (1-31) | `day=1` (每月1号) |
| `weekday` | 星期 (0-6) | `weekday=1` (每周一) |
| `month` | 月份 (1-12) | `month=1` (每年1月) |

**注意**：ARQ **不支持** `second` 参数！

## 常用配置示例

### 1. 每分钟执行

```python
cron(coroutine=task_func, minute=1, run_at_startup=True)
```

### 2. 每5分钟执行

```python
cron(coroutine=task_func, minute=5, run_at_startup=True)
```

### 3. 每小时执行

```python
cron(coroutine=task_func, hour=1, run_at_startup=True)
```

### 4. 每天执行（午夜）

```python
cron(coroutine=task_func, hour=0, minute=0, run_at_startup=True)
```

### 5. 每周一执行

```python
cron(coroutine=task_func, weekday=1, hour=9, minute=0, run_at_startup=True)
```

## 验证修复

### 1. 测试配置

```bash
python almond_parser/test_cron_fix.py
```

### 2. 重启 Worker

```bash
# 停止当前 Worker (Ctrl+C)
python almond_parser/worker.py
```

### 3. 观察日志

修复后应该看到：

```
启动 ARQ 工作器...
Starting worker for...
Registering cron job: cron:health_check_all_nodes
Starting cron job: cron:health_check_all_nodes
```

### 4. 等待执行

- 启动时立即执行一次（`run_at_startup=True`）
- 然后每分钟执行一次

## 配置调整建议

如果需要不同的执行频率：

### 高频监控（每分钟）
```python
minute=1  # 适合开发和测试
```

### 中频监控（每5分钟）
```python
minute=5  # 适合生产环境
```

### 低频监控（每小时）
```python
hour=1  # 适合稳定的生产环境
```

## 注意事项

1. **最小间隔**：ARQ cron 最小间隔是1分钟
2. **启动执行**：`run_at_startup=True` 确保服务启动时立即执行一次
3. **任务重叠**：如果任务执行时间超过间隔时间，ARQ 会等待当前任务完成
4. **错误处理**：任务失败会记录日志，但不会影响下次执行

## 故障排除

### 1. Worker 启动但没有 cron 日志

检查 WorkerSettings.cron_jobs 是否正确配置

### 2. 任务执行失败

检查任务函数是否有异常，查看详细错误日志

### 3. 频率不符合预期

确认 cron 参数设置正确，注意 ARQ 的时间单位限制

### 4. 数据库连接问题

确保任务中使用 `db_manager.session_factory()` 而不是 `get_db()`
