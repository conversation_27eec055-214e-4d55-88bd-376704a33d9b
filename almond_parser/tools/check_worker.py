#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
ARQ Worker 状态检查工具
"""

import asyncio
import sys
import psutil
from pathlib import Path
from loguru import logger

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from almond_parser.tasks.arq_app import arq_manager
from almond_parser.config import settings


def check_worker_process():
    """检查 Worker 进程是否运行"""
    logger.info("🔍 检查 ARQ Worker 进程...")
    
    worker_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('worker.py' in arg for arg in cmdline):
                worker_processes.append({
                    'pid': proc.info['pid'],
                    'cmdline': ' '.join(cmdline)
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if worker_processes:
        logger.info(f"✅ 找到 {len(worker_processes)} 个 Worker 进程:")
        for proc in worker_processes:
            logger.info(f"  PID: {proc['pid']}, 命令: {proc['cmdline']}")
        return True
    else:
        logger.warning("❌ 没有找到 Worker 进程")
        return False


async def check_redis_connection():
    """检查 Redis 连接"""
    logger.info("🔍 检查 Redis 连接...")
    
    try:
        await arq_manager.initialize()
        redis_pool = arq_manager.redis_pool
        
        # 测试 Redis 连接
        await redis_pool.ping()
        logger.info("✅ Redis 连接正常")
        
        # 检查队列状态
        queue_length = await redis_pool.llen('arq:queue')
        logger.info(f"📊 队列长度: {queue_length}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Redis 连接失败: {e}")
        return False


async def check_scheduled_jobs():
    """检查定时任务"""
    logger.info("🔍 检查定时任务配置...")
    
    from almond_parser.tasks.arq_app import WorkerSettings
    
    if hasattr(WorkerSettings, 'cron_jobs') and WorkerSettings.cron_jobs:
        logger.info(f"✅ 配置了 {len(WorkerSettings.cron_jobs)} 个定时任务:")
        for i, cron_job in enumerate(WorkerSettings.cron_jobs):
            logger.info(f"  {i+1}. {cron_job.coroutine.__name__}")
            logger.info(f"     间隔: {getattr(cron_job, 'second', 'N/A')} 秒")
            logger.info(f"     启动时运行: {getattr(cron_job, 'run_at_startup', False)}")
        return True
    else:
        logger.warning("❌ 没有配置定时任务")
        return False


async def test_task_submission():
    """测试任务提交"""
    logger.info("🧪 测试任务提交...")
    
    try:
        await arq_manager.initialize()
        
        # 提交一个测试任务
        job_id = await arq_manager.enqueue_task(
            "health_check_all_nodes"
        )
        
        logger.info(f"✅ 任务提交成功，Job ID: {job_id}")
        return True
    except Exception as e:
        logger.error(f"❌ 任务提交失败: {e}")
        return False


def show_worker_startup_command():
    """显示 Worker 启动命令"""
    logger.info("📋 Worker 启动命令:")
    logger.info("  单独启动: python almond_parser/worker.py")
    logger.info("  统一启动: python almond_parser/start_services.py")


async def main():
    """主函数"""
    logger.info("🔧 ARQ Worker 状态检查工具")
    logger.info("=" * 50)
    
    checks = []
    
    # 检查 Worker 进程
    worker_running = check_worker_process()
    checks.append(("Worker 进程", worker_running))
    
    # 检查 Redis 连接
    redis_ok = await check_redis_connection()
    checks.append(("Redis 连接", redis_ok))
    
    # 检查定时任务配置
    cron_ok = await check_scheduled_jobs()
    checks.append(("定时任务配置", cron_ok))
    
    # 如果 Redis 正常，测试任务提交
    if redis_ok:
        task_ok = await test_task_submission()
        checks.append(("任务提交", task_ok))
    
    # 显示检查结果
    logger.info("\n📊 检查结果汇总:")
    all_ok = True
    for name, status in checks:
        status_text = "✅ 正常" if status else "❌ 异常"
        logger.info(f"  {name}: {status_text}")
        if not status:
            all_ok = False
    
    if not worker_running:
        logger.info("\n💡 解决建议:")
        logger.info("  定时任务需要 ARQ Worker 进程才能执行")
        show_worker_startup_command()
    
    if all_ok:
        logger.info("\n🎉 所有检查通过！定时任务应该正常运行")
    else:
        logger.info("\n⚠️  发现问题，请根据上述信息进行修复")
    
    # 清理连接
    try:
        await arq_manager.close()
    except:
        pass


if __name__ == "__main__":
    asyncio.run(main())
