# -*- encoding: utf-8 -*-
"""
MinerU 节点相关 Pydantic 模型
"""
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_validator
from enum import Enum

from .base import TimestampMixin, IDMixin


class NodeStatus(str, Enum):
    ONLINE = "online"
    OFFLINE = "offline"
    BUSY = "busy"
    ERROR = "error"


class ParseMode(str, Enum):
    PIPELINE = "pipeline"
    SGLANG = "sglang"
    AUTO = "auto"


class ServiceType(str, Enum):
    DOCUMENT = "document"
    KNOWLEDGE_BASE = "knowledge_base"
    UNIVERSAL = "universal"


class MinerUNodeBase(BaseModel):
    """MinerU 节点基础模型"""
    name: str = Field(description="节点名称", max_length=100)
    host: str = Field(description="节点主机地址", max_length=255)
    port: int = Field(description="节点端口", ge=1, le=65535)
    parse_mode: ParseMode = Field(description="支持的解析模式")
    service_type: ServiceType = Field(default=ServiceType.UNIVERSAL, description="节点服务类型")
    max_concurrent_tasks: int = Field(default=3, ge=1, le=20, description="最大并发任务数")
    priority: int = Field(default=1, ge=1, le=10, description="节点优先级")

    @field_validator('host')
    @classmethod
    def validate_host(cls, v):
        if not v or v.strip() == "":
            raise ValueError("主机地址不能为空")
        return v.strip()


class MinerUNodeCreate(MinerUNodeBase):
    """创建 MinerU 节点模型"""
    auth_token: Optional[str] = Field(default=None, description="认证令牌", max_length=500)
    username: Optional[str] = Field(default=None, description="认证用户名", max_length=100)
    password: Optional[str] = Field(default=None, description="认证密码", max_length=500)
    description: Optional[str] = Field(default=None, description="节点描述")
    tags: Optional[Dict[str, Any]] = Field(default=None, description="节点标签")
    health_check_interval: int = Field(default=60, ge=30, le=3600, description="健康检查间隔(秒)")

    @property
    def base_url(self) -> str:
        """生成完整的节点URL"""
        return f"http://{self.host}:{self.port}"


class MinerUNodeUpdate(BaseModel):
    """更新 MinerU 节点模型"""
    name: Optional[str] = Field(default=None, description="节点名称", max_length=100)
    host: Optional[str] = Field(default=None, description="节点主机地址", max_length=255)
    port: Optional[int] = Field(default=None, ge=1, le=65535, description="节点端口")
    parse_mode: Optional[ParseMode] = Field(default=None, description="支持的解析模式")
    service_type: Optional[ServiceType] = Field(default=None, description="节点服务类型")
    max_concurrent_tasks: Optional[int] = Field(default=None, ge=1, le=20, description="最大并发任务数")
    priority: Optional[int] = Field(default=None, ge=1, le=10, description="节点优先级")
    auth_token: Optional[str] = Field(default=None, description="认证令牌", max_length=500)
    username: Optional[str] = Field(default=None, description="认证用户名", max_length=100)
    password: Optional[str] = Field(default=None, description="认证密码", max_length=500)
    description: Optional[str] = Field(default=None, description="节点描述")
    tags: Optional[Dict[str, Any]] = Field(default=None, description="节点标签")
    health_check_interval: Optional[int] = Field(default=None, ge=30, le=3600, description="健康检查间隔(秒)")
    is_enabled: Optional[bool] = Field(default=None, description="是否启用")


class MinerUNodeResponse(MinerUNodeBase, IDMixin, TimestampMixin):
    """MinerU 节点响应模型"""
    base_url: str = Field(description="节点完整URL")
    status: NodeStatus = Field(description="节点状态")
    current_tasks: int = Field(description="当前任务数")
    total_tasks: int = Field(description="总任务数")
    success_tasks: int = Field(description="成功任务数")
    failed_tasks: int = Field(description="失败任务数")
    last_health_check: Optional[datetime] = Field(default=None, description="最后健康检查时间")
    health_check_interval: int = Field(description="健康检查间隔(秒)")
    consecutive_failures: int = Field(description="连续失败次数")
    version: Optional[str] = Field(default=None, description="节点版本")
    description: Optional[str] = Field(default=None, description="节点描述")
    tags: Optional[Dict[str, Any]] = Field(default=None, description="节点标签")
    is_enabled: bool = Field(description="是否启用")

    @property
    def service_type_display(self) -> str:
        """服务类型显示名称"""
        type_map = {
            ServiceType.DOCUMENT: "文档解析",
            ServiceType.KNOWLEDGE_BASE: "知识库解析",
            ServiceType.UNIVERSAL: "通用节点"
        }
        return type_map.get(self.service_type, "未知")

    @property
    def load_percentage(self) -> float:
        """计算负载百分比"""
        if self.max_concurrent_tasks <= 0:
            return 0.0
        return round((self.current_tasks / self.max_concurrent_tasks) * 100, 2)

    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_tasks <= 0:
            return 0.0
        return round((self.success_tasks / self.total_tasks) * 100, 2)

    class Config:
        from_attributes = True


class MinerUNodeStats(BaseModel):
    """MinerU节点统计信息"""
    total_nodes: int = Field(..., description="总节点数")
    online_nodes: int = Field(..., description="在线节点数")
    offline_nodes: int = Field(..., description="离线节点数")
    busy_nodes: int = Field(..., description="繁忙节点数")
    error_nodes: int = Field(..., description="错误节点数")
    total_tasks: int = Field(..., description="总任务数")
    success_tasks: int = Field(..., description="成功任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    running_tasks: int = Field(..., description="运行中任务数")
    success_rate: float = Field(..., description="成功率")


class NodeHealthStatus(BaseModel):
    """节点健康状态"""
    node_id: int = Field(description="节点ID")
    node_name: str = Field(description="节点名称")
    is_healthy: bool = Field(description="是否健康")
    response_time: Optional[int] = Field(default=None, description="响应时间(毫秒)")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    current_tasks: int = Field(default=0, description="当前任务数")
    version: Optional[str] = Field(default=None, description="节点版本")
    parse_mode: Optional[str] = Field(default=None, description="解析模式")
    server_type: Optional[str] = Field(default=None, description="服务器类型")
    sglang_available: Optional[bool] = Field(default=None, description="sglang是否可用")
    checked_at: datetime = Field(description="检查时间")


class HealthCheckResult(BaseModel):
    """健康检查结果"""
    message: str = Field(description="检查消息")
    is_healthy: bool = Field(description="是否健康")
    response_time: Optional[int] = Field(default=None, description="响应时间(毫秒)")


class AutoDetectResult(BaseModel):
    """自动检测结果"""
    message: str = Field(description="检测消息")
    detected_mode: Optional[str] = Field(default=None, description="检测到的模式")
    updated: bool = Field(description="是否已更新")


class NodeListResponse(BaseModel):
    """节点列表响应"""
    items: List[MinerUNodeResponse] = Field(description="节点列表")
    total: int = Field(description="总数量")
    stats: Optional[MinerUNodeStats] = Field(default=None, description="统计信息")
