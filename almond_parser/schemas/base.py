# -*- encoding: utf-8 -*-
"""
基础 Pydantic 模型
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(default=True, description="是否成功")
    message: str = Field(default="操作成功", description="响应消息")
    code: int = Field(default=200, description="响应代码")


class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    
    @property
    def skip(self) -> int:
        """计算跳过的记录数"""
        return (self.page - 1) * self.page_size
    
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.page_size


class PaginationResponse(BaseModel):
    """分页响应模型"""
    total: int = Field(description="总记录数")
    page: int = Field(description="当前页码")
    page_size: int = Field(description="每页数量")
    pages: int = Field(description="总页数")
    
    @classmethod
    def create(cls, total: int, page: int, page_size: int):
        """创建分页响应"""
        pages = (total + page_size - 1) // page_size
        return cls(
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )


class TimestampMixin(BaseModel):
    """时间戳混入"""
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")


class IDMixin(BaseModel):
    """ID混入"""
    id: int = Field(description="主键ID")


class StatusResponse(BaseResponse):
    """状态响应"""
    data: Optional[dict] = Field(default=None, description="响应数据")


class ErrorResponse(BaseResponse):
    """错误响应"""
    success: bool = Field(default=False, description="是否成功")
    error_code: Optional[str] = Field(default=None, description="错误代码")
    details: Optional[dict] = Field(default=None, description="错误详情")
