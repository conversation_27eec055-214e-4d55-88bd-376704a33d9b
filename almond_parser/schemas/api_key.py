# -*- encoding: utf-8 -*-
"""
API Key 相关 Pydantic 模型
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field

from .base import TimestampMixin, IDMixin


class ApiKeyBase(BaseModel):
    """API Key 基础模型"""
    name: str = Field(description="密钥名称", max_length=100)


class ApiKeyCreate(ApiKeyBase):
    """创建 API Key 模型"""
    expires_days: Optional[int] = Field(default=None, ge=1, le=365, description="过期天数")


class ApiKeyUpdate(BaseModel):
    """更新 API Key 模型"""
    name: Optional[str] = Field(default=None, description="密钥名称", max_length=100)
    is_enabled: Optional[bool] = Field(default=None, description="是否启用")


class ApiKeyResponse(ApiKeyBase, IDMixin, TimestampMixin):
    """API Key 响应模型"""
    key: str = Field(description="API密钥")
    user_id: int = Field(description="用户ID")
    is_enabled: bool = Field(description="是否启用")
    last_used_at: Optional[datetime] = Field(default=None, description="最后使用时间")
    expires_at: Optional[datetime] = Field(default=None, description="过期时间")
    usage_count: int = Field(description="使用次数")
    
    class Config:
        from_attributes = True


class ApiKeyListResponse(BaseModel):
    """API Key 列表响应"""
    items: list[ApiKeyResponse] = Field(description="API Key 列表")
    total: int = Field(description="总数量")
