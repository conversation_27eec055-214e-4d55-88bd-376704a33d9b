# Alembic 数据库迁移指南

## 📋 概述

本项目使用 Alembic 进行数据库迁移管理，支持异步数据库操作和自动迁移生成。

## 🚀 快速开始

### 1. 初始化数据库

如果是全新的数据库，使用以下命令初始化：

```bash
# 方法1：使用便捷脚本
python almond_parser/migrate.py init-db

# 方法2：使用 alembic_manager
python almond_parser/alembic_manager.py init-db
```

### 2. 创建迁移

当你修改了数据库模型后，创建新的迁移：

```bash
# 自动生成迁移（推荐）
python almond_parser/migrate.py revision -m "添加新字段"

# 手动创建空迁移
python almond_parser/migrate.py revision --no-autogenerate -m "手动迁移"
```

### 3. 应用迁移

```bash
# 升级到最新版本
python almond_parser/migrate.py upgrade

# 升级到指定版本
python almond_parser/migrate.py upgrade <revision_id>
```

### 4. 查看状态

```bash
# 查看当前状态
python almond_parser/migrate.py status

# 查看当前版本
python almond_parser/migrate.py current

# 查看迁移历史
python almond_parser/migrate.py history
```

## 🔧 常用命令

### 基础命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `init-db` | 初始化数据库 | `python migrate.py init-db` |
| `revision` | 创建新迁移 | `python migrate.py revision -m "描述"` |
| `upgrade` | 升级数据库 | `python migrate.py upgrade` |
| `downgrade` | 降级数据库 | `python migrate.py downgrade -1` |
| `current` | 显示当前版本 | `python migrate.py current` |
| `history` | 显示迁移历史 | `python migrate.py history` |
| `status` | 显示详细状态 | `python migrate.py status` |

### 高级命令

| 命令 | 说明 | 示例 |
|------|------|------|
| `auto-migrate` | 自动检测并迁移 | `python migrate.py auto-migrate` |
| `check` | 检查同步状态 | `python migrate.py check` |
| `show` | 显示迁移详情 | `python migrate.py show head` |
| `reset-db` | 重置数据库（危险） | `python migrate.py reset-db` |

## 📝 工作流程

### 开发环境

1. **修改模型**：在 `almond_parser/db/models/` 中修改数据库模型
2. **创建迁移**：`python migrate.py revision -m "描述变更"`
3. **检查迁移**：查看生成的迁移文件，确认变更正确
4. **应用迁移**：`python migrate.py upgrade`
5. **测试验证**：确认数据库变更正确

### 生产环境

1. **备份数据库**：在应用迁移前备份生产数据库
2. **检查状态**：`python migrate.py status`
3. **应用迁移**：`python migrate.py upgrade`
4. **验证结果**：确认迁移成功且应用正常运行

## 🔍 迁移文件结构

```
almond_parser/alembic/
├── alembic.ini          # Alembic 配置文件
├── env.py              # 环境配置（已配置异步支持）
├── script.py.mako      # 迁移文件模板
└── versions/           # 迁移文件目录
    ├── 20241226_1430_001_initial_migration.py
    └── 20241226_1445_002_add_retry_fields.py
```

## ⚙️ 配置说明

### 数据库连接

数据库连接配置在 `almond_parser/config.py` 中：

```python
# MySQL 配置
MYSQL_HOST: str = "localhost"
MYSQL_PORT: int = 3306
MYSQL_USER: str = "root"
MYSQL_PASSWORD: str = "123456"
MYSQL_DATABASE: str = "almond_parser"
```

### 环境变量

可以通过环境变量覆盖配置：

```bash
export DATABASE_URL="mysql+aiomysql://user:pass@host:port/dbname"
export MYSQL_HOST="your-mysql-host"
export MYSQL_PASSWORD="your-password"
```

## 🚨 注意事项

### 1. 备份数据

在生产环境应用迁移前，务必备份数据库：

```bash
mysqldump -u root -p almond_parser > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 检查迁移

创建迁移后，务必检查生成的迁移文件：

```python
# 检查 alembic/versions/ 中的最新迁移文件
# 确认 upgrade() 和 downgrade() 函数正确
```

### 3. 测试迁移

在开发环境测试迁移的完整流程：

```bash
# 应用迁移
python migrate.py upgrade

# 测试回滚
python migrate.py downgrade -1

# 重新应用
python migrate.py upgrade
```

### 4. 并发迁移

避免多个实例同时执行迁移，使用锁机制或确保单实例执行。

## 🔧 故障排除

### 常见问题

1. **循环导入错误**
   ```bash
   # 确保项目路径正确
   export PYTHONPATH=/path/to/aicenter-parserflow
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库配置
   python migrate.py status
   ```

3. **迁移冲突**
   ```bash
   # 查看迁移历史
   python migrate.py history
   # 手动解决冲突后重新创建迁移
   ```

### 重置数据库

如果遇到严重问题，可以重置数据库（开发环境）：

```bash
# 警告：这会删除所有数据和迁移历史
python migrate.py reset-db
```

## 📚 最佳实践

1. **描述性消息**：创建迁移时使用清晰的描述信息
2. **小步迁移**：避免在单个迁移中进行过多变更
3. **测试优先**：在开发环境充分测试后再应用到生产环境
4. **版本控制**：将迁移文件纳入版本控制
5. **文档记录**：重要的数据库变更要有文档记录

## 🎯 自动化

### CI/CD 集成

```yaml
# 示例 GitHub Actions
- name: Run Database Migrations
  run: |
    python almond_parser/migrate.py upgrade
```

### 定期检查

```bash
# 定期检查数据库同步状态
python migrate.py check
```

这样配置后，您就可以方便地管理数据库迁移了！
