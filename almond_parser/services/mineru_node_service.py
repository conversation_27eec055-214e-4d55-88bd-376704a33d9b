# -*- encoding: utf-8 -*-
"""
MinerU 节点服务
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, desc
from loguru import logger

from almond_parser.db.models import MinerUNode, NodeHealthCheck, NodeStatus, ParseMode, ServiceType
from almond_parser.schemas.mineru_node import (
    MinerUNodeCreate, MinerUNodeUpdate, MinerUNodeResponse, 
    MinerUNodeStats, NodeHealthStatus
)


class MinerUNodeService:
    """MinerU 节点服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_node(self, node_data: MinerUNodeCreate) -> MinerUNodeResponse:
        """创建节点"""
        try:
            # 生成完整的 base_url
            base_url = f"http://{node_data.host}:{node_data.port}"
            
            # 检查是否已存在相同的 base_url
            existing = await self.db.execute(
                select(MinerUNode).where(MinerUNode.base_url == base_url)
            )
            if existing.scalar_one_or_none():
                raise ValueError(f"节点 {base_url} 已存在")
            
            # 创建节点
            node = MinerUNode(
                name=node_data.name,
                host=node_data.host,
                port=node_data.port,
                base_url=base_url,
                parse_mode=node_data.parse_mode,
                service_type=node_data.service_type,
                max_concurrent_tasks=node_data.max_concurrent_tasks,
                priority=node_data.priority,
                auth_token=node_data.auth_token,
                username=node_data.username,
                password=node_data.password,
                description=node_data.description,
                tags=node_data.tags,
                health_check_interval=node_data.health_check_interval,
                status=NodeStatus.OFFLINE,
                is_enabled=True
            )
            
            self.db.add(node)
            await self.db.commit()
            await self.db.refresh(node)
            
            logger.info(f"创建节点成功: {node.name} ({node.base_url})")
            return MinerUNodeResponse.model_validate(node)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建节点失败: {e}")
            raise
    
    async def get_nodes(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[NodeStatus] = None,
        parse_mode: Optional[ParseMode] = None,
        service_type: Optional[ServiceType] = None,
        is_enabled: Optional[bool] = None
    ) -> List[MinerUNodeResponse]:
        """获取节点列表"""
        try:
            conditions = []
            
            if status:
                conditions.append(MinerUNode.status == status)
            if parse_mode:
                conditions.append(MinerUNode.parse_mode == parse_mode)
            if service_type:
                conditions.append(MinerUNode.service_type == service_type)
            if is_enabled is not None:
                conditions.append(MinerUNode.is_enabled == is_enabled)
            
            query = select(MinerUNode)
            if conditions:
                query = query.where(and_(*conditions))
            
            query = query.order_by(desc(MinerUNode.priority), MinerUNode.created_at)
            query = query.offset(skip).limit(limit)
            
            result = await self.db.execute(query)
            nodes = result.scalars().all()
            
            return [MinerUNodeResponse.model_validate(node) for node in nodes]
            
        except Exception as e:
            logger.error(f"获取节点列表失败: {e}")
            raise
    
    async def get_node(self, node_id: int) -> Optional[MinerUNodeResponse]:
        """获取单个节点"""
        try:
            result = await self.db.execute(
                select(MinerUNode).where(MinerUNode.id == node_id)
            )
            node = result.scalar_one_or_none()
            
            if node:
                return MinerUNodeResponse.model_validate(node)
            return None
            
        except Exception as e:
            logger.error(f"获取节点失败: {e}")
            raise
    
    async def update_node(
        self, 
        node_id: int, 
        node_data: MinerUNodeUpdate
    ) -> Optional[MinerUNodeResponse]:
        """更新节点"""
        try:
            result = await self.db.execute(
                select(MinerUNode).where(MinerUNode.id == node_id)
            )
            node = result.scalar_one_or_none()
            
            if not node:
                return None
            
            # 更新字段
            update_fields = node_data.dict(exclude_unset=True)
            for field, value in update_fields.items():
                if hasattr(node, field):
                    setattr(node, field, value)
            
            # 如果更新了 host 或 port，重新生成 base_url
            if 'host' in update_fields or 'port' in update_fields:
                node.base_url = f"http://{node.host}:{node.port}"
            
            await self.db.commit()
            await self.db.refresh(node)
            
            logger.info(f"更新节点成功: {node.name}")
            return MinerUNodeResponse.model_validate(node)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新节点失败: {e}")
            raise
    
    async def delete_node(self, node_id: int) -> bool:
        """删除节点"""
        try:
            result = await self.db.execute(
                select(MinerUNode).where(MinerUNode.id == node_id)
            )
            node = result.scalar_one_or_none()
            
            if not node:
                return False
            
            # 删除相关的健康检查记录
            await self.db.execute(
                select(NodeHealthCheck).where(NodeHealthCheck.node_id == node_id)
            )
            
            # 删除节点
            await self.db.delete(node)
            await self.db.commit()
            
            logger.info(f"删除节点成功: {node.name}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除节点失败: {e}")
            raise
    
    async def get_node_stats(self) -> MinerUNodeStats:
        """获取节点统计信息"""
        try:
            # 获取所有节点
            result = await self.db.execute(select(MinerUNode))
            nodes = result.scalars().all()
            
            total_nodes = len(nodes)
            online_nodes = sum(1 for node in nodes if node.status == NodeStatus.ONLINE)
            offline_nodes = sum(1 for node in nodes if node.status == NodeStatus.OFFLINE)
            busy_nodes = sum(1 for node in nodes if node.status == NodeStatus.BUSY)
            error_nodes = sum(1 for node in nodes if node.status == NodeStatus.ERROR)
            
            total_tasks = sum(node.total_tasks for node in nodes)
            running_tasks = sum(node.current_tasks for node in nodes)
            completed_tasks = sum(node.success_tasks for node in nodes)
            failed_tasks = sum(node.failed_tasks for node in nodes)
            
            # 计算平均负载
            if total_nodes > 0:
                total_load = sum(
                    (node.current_tasks / max(node.max_concurrent_tasks, 1)) * 100 
                    for node in nodes
                )
                average_load = total_load / total_nodes
                
                # 计算平均成功率
                total_success_rate = sum(
                    (node.success_tasks / max(node.total_tasks, 1)) * 100 
                    for node in nodes
                )
                average_success_rate = total_success_rate / total_nodes
            else:
                average_load = 0.0
                average_success_rate = 0.0
                
            return MinerUNodeStats(
                total_nodes=total_nodes,
                online_nodes=online_nodes,
                offline_nodes=offline_nodes,
                busy_nodes=busy_nodes,
                error_nodes=error_nodes,
                total_tasks=total_tasks,
                running_tasks=running_tasks,
                completed_tasks=completed_tasks,
                failed_tasks=failed_tasks,
                average_load=average_load,
                average_success_rate=average_success_rate
            )
            
        except Exception as e:
            logger.error(f"获取节点统计信息失败: {e}")
            raise

    async def toggle_node_status(self, node_id: int, enabled: bool) -> MinerUNodeResponse:
        """切换节点启用状态"""
        try:
            # 获取节点
            result = await self.db.execute(
                select(MinerUNode).where(MinerUNode.id == node_id)
            )
            node = result.scalar_one_or_none()
            
            if not node:
                raise ValueError(f"节点 {node_id} 不存在")
            
            # 更新状态
            node.is_enabled = enabled
            
            # 如果禁用节点，同时将状态设置为离线
            if not enabled:
                node.status = NodeStatus.OFFLINE
            
            await self.db.commit()
            await self.db.refresh(node)
            
            logger.info(f"节点 {node.name} ({node.id}) 已{'启用' if enabled else '禁用'}")
            return MinerUNodeResponse.model_validate(node)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"切换节点状态失败: {e}")
            raise
    
    async def get_node_health_status(self, node_id: int) -> Optional[NodeHealthStatus]:
        """获取节点健康状态"""
        try:
            # 获取最新的健康检查记录
            result = await self.db.execute(
                select(NodeHealthCheck)
                .where(NodeHealthCheck.node_id == node_id)
                .order_by(desc(NodeHealthCheck.checked_at))
                .limit(1)
            )
            health_check = result.scalar_one_or_none()
            
            if not health_check:
                return None
            
            # 获取节点信息
            node_result = await self.db.execute(
                select(MinerUNode).where(MinerUNode.id == node_id)
            )
            node = node_result.scalar_one_or_none()
            
            if not node:
                return None
            
            return NodeHealthStatus(
                node_id=node_id,
                node_name=node.name,
                is_healthy=health_check.is_healthy,
                response_time=health_check.response_time,
                error_message=health_check.error_message,
                current_tasks=health_check.current_tasks or 0,
                version=health_check.node_version,
                parse_mode=node.parse_mode.value,
                checked_at=health_check.checked_at
            )
            
        except Exception as e:
            logger.error(f"获取节点健康状态失败: {e}")
            raise
