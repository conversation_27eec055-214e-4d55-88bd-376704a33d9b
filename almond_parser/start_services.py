#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
杏仁解析服务启动脚本
同时启动 API 服务和 ARQ Worker
"""

import asyncio
import subprocess
import signal
import sys
import time
from pathlib import Path
from loguru import logger

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.processes = []
        self.running = True
        
    def start_api_server(self):
        """启动 API 服务"""
        logger.info("🚀 启动 API 服务...")
        
        cmd = [
            sys.executable, "-m", "uvicorn",
            "almond_parser.main:app",
            "--host", "0.0.0.0",
            "--port", "8010",
            "--reload"
        ]
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        self.processes.append(("API Server", process))
        logger.info(f"✅ API 服务已启动，PID: {process.pid}")
        return process
    
    def start_worker(self):
        """启动 ARQ Worker"""
        logger.info("🔧 启动 ARQ Worker...")
        
        cmd = [sys.executable, "almond_parser/worker.py"]
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        self.processes.append(("ARQ Worker", process))
        logger.info(f"✅ ARQ Worker 已启动，PID: {process.pid}")
        return process
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            for name, process in self.processes:
                if process.poll() is not None:
                    logger.error(f"❌ {name} 进程异常退出，退出码: {process.returncode}")
                    # 读取错误输出
                    if process.stdout:
                        output = process.stdout.read()
                        if output:
                            logger.error(f"{name} 输出: {output}")
                    
                    # 重启进程
                    logger.info(f"🔄 重启 {name}...")
                    if name == "API Server":
                        new_process = self.start_api_server()
                    else:
                        new_process = self.start_worker()
                    
                    # 更新进程列表
                    self.processes = [(n, p) for n, p in self.processes if p != process]
                    self.processes.append((name, new_process))
            
            time.sleep(5)  # 每5秒检查一次
    
    def stop_all(self):
        """停止所有服务"""
        logger.info("🛑 停止所有服务...")
        self.running = False
        
        for name, process in self.processes:
            if process.poll() is None:
                logger.info(f"停止 {name} (PID: {process.pid})")
                try:
                    process.terminate()
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning(f"强制杀死 {name}")
                    process.kill()
                    process.wait()
        
        logger.info("👋 所有服务已停止")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，准备停止服务...")
        self.stop_all()
        sys.exit(0)


def main():
    """主函数"""
    logger.info("🌟 杏仁解析服务管理器")
    logger.info("=" * 50)
    
    manager = ServiceManager()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, manager.signal_handler)
    signal.signal(signal.SIGTERM, manager.signal_handler)
    
    try:
        # 启动服务
        api_process = manager.start_api_server()
        worker_process = manager.start_worker()
        
        logger.info("🎉 所有服务启动完成")
        logger.info("📋 服务列表:")
        logger.info("  - API 服务: http://localhost:8010")
        logger.info("  - API 文档: http://localhost:8010/docs")
        logger.info("  - ARQ Worker: 后台运行")
        logger.info("\n按 Ctrl+C 停止所有服务")
        
        # 监控进程
        manager.monitor_processes()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号...")
        manager.stop_all()
    except Exception as e:
        logger.error(f"服务管理器异常: {e}")
        manager.stop_all()
        sys.exit(1)


if __name__ == "__main__":
    main()
