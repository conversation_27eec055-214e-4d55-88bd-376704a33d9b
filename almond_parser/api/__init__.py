# -*- encoding: utf-8 -*-
"""
API 路由模块
"""
from fastapi import APIRouter

from .api_keys import router as api_keys_router
from .documents import router as documents_router
from .mineru_nodes import router as mineru_nodes_router
from .websocket import router as websocket_router
from .manage import router as manage_router
from .dashboard import router as dashboard_router

# 创建主路由
api_router = APIRouter(prefix="/api/v1")

# 注册子路由
api_router.include_router(api_keys_router)
api_router.include_router(documents_router)
api_router.include_router(mineru_nodes_router)
api_router.include_router(websocket_router)
api_router.include_router(manage_router)
api_router.include_router(dashboard_router)

__all__ = ["api_router"]
