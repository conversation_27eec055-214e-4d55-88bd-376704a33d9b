# -*- encoding: utf-8 -*-
"""
WebSocket API - 实时日志推送
"""
import asyncio
import json
from typing import Dict, Set
from pathlib import Path
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from almond_parser.db import get_db
from almond_parser.services.document_service import DocumentService
from almond_parser.utils.auth import verify_token
from almond_parser.config import settings

router = APIRouter(prefix="/manage/ws", tags=["WebSocket"])


async def monitor_log_file(websocket: WebSocket, document_id: str):
    """监控日志文件并推送相关日志"""
    log_file_path = Path(settings.LOG_DIR) / "almond_parser.log"

    try:
        if not log_file_path.exists():
            await websocket.send_text(json.dumps({
                "type": "log",
                "level": "warning",
                "message": f"日志文件不存在: {log_file_path}",
                "timestamp": asyncio.get_event_loop().time()
            }, ensure_ascii=False))
            return

        # 跳到文件末尾，只监控新日志
        with open(log_file_path, "r", encoding="utf-8") as f:
            f.seek(0, 2)  # 跳到文件末尾

            while True:
                line = f.readline()
                if line:
                    # 检查日志行是否与当前文档相关
                    if f"[doc:{document_id}]" in line or f"document_id={document_id}" in line:
                        await websocket.send_text(json.dumps({
                            "type": "log",
                            "level": "info",
                            "message": line.strip(),
                            "timestamp": asyncio.get_event_loop().time(),
                            "source": "file"
                        }, ensure_ascii=False))
                else:
                    # 没有新日志，等待一下
                    await asyncio.sleep(0.5)

    except asyncio.CancelledError:
        logger.info(f"文档 {document_id} 的日志监控任务已取消")
        raise
    except Exception as e:
        logger.error(f"监控日志文件异常: {e}")
        try:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"日志监控异常: {str(e)}",
                "timestamp": asyncio.get_event_loop().time()
            }, ensure_ascii=False))
        except:
            pass


class ConnectionManager:
    """WebSocket 连接管理器"""

    def __init__(self):
        # 存储活跃连接: {document_id: {websocket1, websocket2, ...}}
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # 存储连接对应的文档ID: {websocket: document_id}
        self.connection_documents: Dict[WebSocket, str] = {}

    async def connect(self, websocket: WebSocket, document_id: str):
        """建立连接"""
        await websocket.accept()

        if document_id not in self.active_connections:
            self.active_connections[document_id] = set()

        self.active_connections[document_id].add(websocket)
        self.connection_documents[websocket] = document_id

        logger.info(f"WebSocket 连接建立: 文档 {document_id}, 当前连接数: {len(self.active_connections[document_id])}")

    def disconnect(self, websocket: WebSocket):
        """断开连接"""
        if websocket in self.connection_documents:
            document_id = self.connection_documents[websocket]

            # 从文档连接集合中移除
            if document_id in self.active_connections:
                self.active_connections[document_id].discard(websocket)

                # 如果该文档没有连接了，删除记录
                if not self.active_connections[document_id]:
                    del self.active_connections[document_id]

            # 从连接映射中移除
            del self.connection_documents[websocket]

            logger.info(f"WebSocket 连接断开: 文档 {document_id}")

    async def send_to_document(self, document_id: str, message: dict):
        """向指定文档的所有连接发送消息"""
        if document_id in self.active_connections:
            disconnected = set()

            for websocket in self.active_connections[document_id]:
                try:
                    await websocket.send_text(json.dumps(message, ensure_ascii=False))
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")
                    disconnected.add(websocket)

            # 清理断开的连接
            for websocket in disconnected:
                self.disconnect(websocket)

    async def broadcast(self, message: dict):
        """广播消息到所有连接"""
        for document_id in list(self.active_connections.keys()):
            await self.send_to_document(document_id, message)

    def get_connection_count(self, document_id: str = None) -> int:
        """获取连接数"""
        if document_id:
            return len(self.active_connections.get(document_id, set()))
        return sum(len(connections) for connections in self.active_connections.values())


# 全局连接管理器
manager = ConnectionManager()


async def verify_websocket_auth(websocket: WebSocket, token: str) -> dict:
    """验证 WebSocket 认证"""
    try:
        payload = verify_token(token)
        if payload is None:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid token")
            raise HTTPException(status_code=401, detail="Invalid token")

        username = payload.get("sub")
        if not username:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid token payload")
            raise HTTPException(status_code=401, detail="Invalid token payload")

        # 从数据库获取用户信息
        from almond_parser.db.models import User
        from almond_parser.db.database import get_async_session
        from sqlalchemy import select

        # 使用正确的异步会话上下文管理器
        async with get_async_session() as db:
            result = await db.execute(select(User).where(User.username == username))
            user = result.scalar_one_or_none()

            if not user:
                await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="User not found")
                raise HTTPException(status_code=401, detail="User not found")

            return {
                "user_id": user.id,
                "username": user.username,
                "is_admin": user.is_admin
            }

    except Exception as e:
        logger.error(f"WebSocket认证失败: {e}")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Authentication failed")
        raise HTTPException(status_code=401, detail="Authentication failed")


@router.websocket("/logs/document/{document_id}")
async def websocket_document_logs(
    websocket: WebSocket,
    document_id: str,
    token: str = None
):
    """
    文档日志 WebSocket 连接

    URL: ws://localhost:8010/api/v1/manage/ws/logs/document/{document_id}?token=your_jwt_token
    """
    # 验证认证
    if not token:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Missing token")
        return

    try:
        user_info = await verify_websocket_auth(websocket, token)
    except:
        return

    # 验证文档权限
    try:
        from almond_parser.db.database import get_async_session
        async with get_async_session() as db:
            service = DocumentService(db)
            document = await service.get_document(document_id, str(user_info["user_id"]))

            if not document:
                await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Document not found")
                return
    except Exception as e:
        logger.error(f"验证文档权限失败: {e}")
        await websocket.close(code=status.WS_1011_INTERNAL_ERROR, reason="Internal error")
        return

    # 建立连接
    await manager.connect(websocket, document_id)

    try:
        # 发送欢迎消息
        await websocket.send_text(json.dumps({
            "type": "connected",
            "message": f"已连接到文档 {document_id} 的日志流",
            "document_id": document_id,
            "timestamp": asyncio.get_event_loop().time()
        }, ensure_ascii=False))

        # 发送历史日志
        from almond_parser.db.database import get_async_session
        async with get_async_session() as db:
            service = DocumentService(db)
            recent_logs = await service.get_document_logs(document_id, limit=50)

            if recent_logs:
                history_data = {
                    "type": "history",
                    "logs": [
                        {
                            "timestamp": log.timestamp.isoformat(),
                            "level": log.level,
                            "message": log.message,
                            "source": log.source
                        }
                        for log in recent_logs
                    ]
                }
                await websocket.send_text(json.dumps(history_data, ensure_ascii=False))

        # 启动实时日志监控任务
        log_task = asyncio.create_task(monitor_log_file(websocket, document_id))

        # 保持连接活跃
        try:
            while True:
                try:
                    # 等待客户端消息（心跳等）
                    data = await asyncio.wait_for(websocket.receive_text(), timeout=1.0)
                    message = json.loads(data)

                    if message.get("type") == "ping":
                        await websocket.send_text(json.dumps({
                            "type": "pong",
                            "timestamp": asyncio.get_event_loop().time()
                        }))

                except asyncio.TimeoutError:
                    # 超时是正常的，继续循环
                    continue
                except WebSocketDisconnect:
                    break
                except json.JSONDecodeError:
                    logger.warning(f"收到无效JSON消息: {data}")
                except Exception as e:
                    logger.error(f"WebSocket 处理消息异常: {e}")
                    break
        finally:
            # 取消日志监控任务
            log_task.cancel()
            try:
                await log_task
            except asyncio.CancelledError:
                pass

    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket 连接异常: {e}")
    finally:
        manager.disconnect(websocket)


@router.websocket("/status")
async def websocket_status(websocket: WebSocket, token: str = None):
    """
    系统状态 WebSocket 连接

    URL: ws://localhost:8010/api/v1/manage/ws/status?token=your_jwt_token
    """
    # 验证认证
    if not token:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Missing token")
        return

    try:
        user_info = await verify_websocket_auth(websocket, token)
    except:
        return

    await websocket.accept()

    try:
        # 发送欢迎消息
        await websocket.send_text(json.dumps({
            "type": "connected",
            "message": "已连接到系统状态流",
            "user": user_info["username"]
        }, ensure_ascii=False))

        # 定期发送状态信息
        while True:
            try:
                # 获取系统状态
                status_info = {
                    "type": "status",
                    "timestamp": asyncio.get_event_loop().time(),
                    "connections": manager.get_connection_count(),
                    "active_documents": len(manager.active_connections)
                }

                await websocket.send_text(json.dumps(status_info, ensure_ascii=False))

                # 等待30秒
                await asyncio.sleep(30)

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"发送状态信息异常: {e}")
                break

    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"状态 WebSocket 连接异常: {e}")


# 提供给其他模块使用的日志推送函数
async def push_document_log(document_id: str, level: str, message: str, source: str = None):
    """推送文档日志到 WebSocket 客户端"""
    log_message = {
        "type": "log",
        "document_id": document_id,
        "timestamp": asyncio.get_event_loop().time(),
        "level": level,
        "message": message,
        "source": source
    }

    await manager.send_to_document(document_id, log_message)


async def push_system_notification(message: str, level: str = "info"):
    """推送系统通知"""
    notification = {
        "type": "notification",
        "timestamp": asyncio.get_event_loop().time(),
        "level": level,
        "message": message
    }

    await manager.broadcast(notification)
