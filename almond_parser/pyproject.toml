[project]
name = "mineru-api"
version = "1.0.0"
description = "基于 LitServe 的 OCR 解析服务"
authors = [
    {name = "MineruAPI Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"

dependencies = [
    # Web框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",

    # 数据库
    "sqlalchemy>=2.0.0",
    "aiomysql>=0.2.0",
    "pymysql>=1.1.0",

    # Redis
    "aioredis>=2.0.0",

    # 异步任务队列
    "arq>=0.25.0",

    # 数据验证
    "pydantic>=2.0.0",
    "pydantic-settings>=2.10.1",

    # 日志
    "loguru>=0.7.0",

    # 认证
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",

    # 文件处理
    "python-magic>=0.4.27",

    # HTTP客户端
    "aiohttp>=3.9.0",
    "httpx>=0.25.0",

    # 工具
    "python-dotenv>=1.0.0"
]

[project.optional-dependencies]
windows = [
    "comtypes>=1.1.14",
    "mineru[core]>=2.0.6",
]
linux = [
    "mineru[all]>=2.0.6",
]

[tool.uv]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"

dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0"
]
