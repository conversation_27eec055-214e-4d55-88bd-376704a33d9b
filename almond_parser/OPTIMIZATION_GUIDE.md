# 资源保护型节点分配优化指南

## 🎯 优化概述

本次优化实现了智能的资源保护型节点分配策略，解决了以下核心问题：

1. **资源保护**：pipe请求优先使用CPU节点，保护GPU资源
2. **智能降级**：sglang解析失败时自动切换到pipeline模式
3. **自动重试**：轮询机制处理节点不可用情况
4. **质量检查**：验证解析结果，确保输出质量

## 📋 核心优化内容

### 1. 节点选择器优化 (`utils/node_selector.py`)

#### 新增方法：
- `_find_pipeline_optimized_node()`: 优化的pipeline节点查找
- `_find_vlm_optimized_node()`: 优化的VLM节点查找
- `_find_pure_pipeline_node()`: 查找纯pipeline节点
- `_find_sglang_node()`: 查找sglang节点

#### 资源保护策略：
```python
# pipe请求的分配策略
if original_mode in ["pipeline", "pipe"]:
    # 1. 优先查找纯pipeline节点（CPU节点）
    # 2. 如果没有，再分配给sglang节点
    return await self._find_pipeline_optimized_node(service_type, request_mode)

# vlm/sglang请求的分配策略  
else:
    # 直接查找sglang节点（GPU节点）
    return await self._find_vlm_optimized_node(service_type, request_mode)
```

### 2. 增强文档处理任务 (`tasks/enhanced_document_tasks.py`)

#### 新增功能：
- **解析结果质量检查**：`_check_result_quality()`
- **自动降级重试**：`_handle_poor_quality_result()`
- **智能错误处理**：`_handle_parsing_failure()`
- **详细日志记录**：包含节点分配详情和资源保护信息

#### 降级重试机制：
```python
# 当sglang解析失败或结果质量不佳时
if can_fallback:
    job_id = await arq_manager.enqueue_task(
        "enhanced_process_document",
        document_id=document.document_id,
        parse_mode="pipeline",  # 降级到pipeline模式
        is_fallback_retry=True
    )
```

### 3. 重试任务优化 (`tasks/retry_tasks.py`)

#### 核心功能：
- **定时轮询**：每5分钟检查需要重试的文档
- **智能重试**：使用增强的节点选择器
- **指数退避**：重试间隔逐渐增加
- **过期清理**：每小时清理超过重试次数的记录

### 4. 数据库模型扩展 (`db/models/document.py`)

#### 新增字段：
```python
# 降级重试信息
original_parse_mode = Column(String(50), nullable=True, comment="原始解析模式")
current_parse_mode = Column(String(50), nullable=True, comment="当前解析模式")
has_fallback = Column(Boolean, default=False, comment="是否已降级")

# 重试类型标记
retry_reason = Column(String(100), nullable=True, comment="重试原因")
is_system_retry = Column(Boolean, default=False, comment="是否为系统重试")
next_retry_at = Column(DateTime, nullable=True, comment="下次重试时间")
```

### 5. ARQ任务配置 (`tasks/arq_app.py`)

#### 新增任务：
- `enhanced_process_document`: 增强的文档处理
- `enhanced_process_document_result`: 增强的结果处理
- `process_retry_documents`: 重试文档处理
- `cleanup_old_retry_records`: 清理过期记录

#### 定时任务：
```python
# 每5分钟检查重试文档
cron(coroutine=retry_tasks.process_retry_documents, minute={0,5,10,15,20,25,30,35,40,45,50,55})

# 每小时清理过期记录
cron(coroutine=retry_tasks.cleanup_old_retry_records, minute=0)
```

## 🚀 使用方法

### 1. 启用增强处理

```python
# 使用增强的文档处理任务
job_id = await arq_manager.enqueue_task(
    'enhanced_process_document',
    document_id=document_id,
    user_id=user_id,
    service_type="auto",
    parse_mode="auto",  # 系统会智能分配节点
    config={}
)
```

### 2. 节点配置建议

#### CPU节点配置（专门处理pipe请求）：
```python
node = MinerUNode(
    name="cpu-pipeline-node-1",
    parse_mode=ParseMode.PIPELINE,
    service_type=ServiceType.DOCUMENT,  # 或 UNIVERSAL
    # ... 其他配置
)
```

#### GPU节点配置（处理vlm/sglang请求）：
```python
node = MinerUNode(
    name="gpu-sglang-node-1", 
    parse_mode=ParseMode.SGLANG,
    service_type=ServiceType.DOCUMENT,  # 或 UNIVERSAL
    # ... 其他配置
)
```

### 3. 监控和日志

系统会自动记录详细的操作日志：
- 节点分配策略（资源保护/降级使用）
- 解析模式切换（sglang -> pipeline）
- 重试原因和时间安排
- 质量检查结果

## 📊 优化效果

### 资源利用优化：
- ✅ CPU节点专门处理pipe请求，减轻GPU压力
- ✅ GPU节点专注处理vlm/sglang请求，提高效率
- ✅ 智能降级确保解析成功率

### 容错能力增强：
- ✅ 自动重试机制处理节点不可用
- ✅ 质量检查确保输出可靠性
- ✅ 详细日志便于问题追踪

### 系统稳定性：
- ✅ 指数退避避免重试风暴
- ✅ 过期清理防止任务堆积
- ✅ 轻量级设计保持高性能

## 🔧 部署注意事项

### 1. 数据库迁移
需要执行数据库迁移以添加新字段：
```sql
ALTER TABLE documents ADD COLUMN original_parse_mode VARCHAR(50);
ALTER TABLE documents ADD COLUMN current_parse_mode VARCHAR(50);
ALTER TABLE documents ADD COLUMN has_fallback BOOLEAN DEFAULT FALSE;
ALTER TABLE documents ADD COLUMN retry_reason VARCHAR(100);
ALTER TABLE documents ADD COLUMN is_system_retry BOOLEAN DEFAULT FALSE;
ALTER TABLE documents ADD COLUMN next_retry_at DATETIME;
```

### 2. ARQ工作器重启
更新ARQ配置后需要重启工作器以加载新任务。

### 3. 节点配置
建议配置专门的CPU节点处理pipeline请求，以实现最佳的资源保护效果。

## 🎉 总结

本次优化实现了完整的资源保护型节点分配策略，通过智能的节点选择、自动降级重试和完善的错误处理，显著提升了系统的稳定性和资源利用效率。

核心优势：
- 🛡️ **资源保护**：保护GPU资源，提高整体效率
- 🔄 **智能降级**：确保解析成功率
- ⏰ **自动重试**：处理节点不可用情况
- 📝 **详细日志**：便于监控和调试

系统现在能够智能地根据请求类型分配最合适的节点，在保护宝贵GPU资源的同时，确保所有解析请求都能得到妥善处理。
