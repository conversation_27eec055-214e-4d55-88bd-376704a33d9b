#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
ARQ 工作器启动脚本
"""
import sys
from pathlib import Path

# 将项目根目录添加到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import asyncio
from arq import run_worker
from almond_parser.tasks.arq_app import WorkerSettings
from almond_parser.utils.logger import setup_logger
from loguru import logger

# 顶层直接调用
if __name__ == "__main__":
    setup_logger()
    logger.info("🚀 启动 ARQ 工作器...")

    try:
        # 运行 worker（数据库初始化在 WorkerSettings 的 on_startup 中处理）
        asyncio.run(run_worker(WorkerSettings))
    except KeyboardInterrupt:
        logger.info("🛑 工作器已手动停止")
    except Exception as e:
        logger.exception(f"❌ 工作器运行异常: {e}")
        raise
