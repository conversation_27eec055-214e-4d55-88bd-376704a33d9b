# -*- encoding: utf-8 -*-
"""
数据库查询优化工具
"""
import time
from typing import Any, Dict, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from loguru import logger


class DatabaseOptimizer:
    """数据库优化器"""
    
    @staticmethod
    async def analyze_slow_queries(db: AsyncSession) -> List[Dict[str, Any]]:
        """分析慢查询"""
        try:
            # 检查MySQL慢查询日志状态
            result = await db.execute(text("SHOW VARIABLES LIKE 'slow_query_log'"))
            slow_log_status = result.fetchone()
            
            # 获取当前连接信息
            result = await db.execute(text("SHOW PROCESSLIST"))
            processes = result.fetchall()
            
            # 获取表状态信息
            result = await db.execute(text("SHOW TABLE STATUS FROM almond_parser"))
            tables = result.fetchall()
            
            return {
                "slow_log_enabled": slow_log_status[1] if slow_log_status else "OFF",
                "active_connections": len(processes),
                "processes": [dict(row._mapping) for row in processes],
                "tables": [dict(row._mapping) for row in tables]
            }
            
        except Exception as e:
            logger.error(f"分析数据库状态失败: {e}")
            return {}
    
    @staticmethod
    async def check_indexes(db: AsyncSession) -> Dict[str, Any]:
        """检查索引使用情况"""
        try:
            # 检查documents表的索引
            result = await db.execute(text("SHOW INDEX FROM documents"))
            indexes = result.fetchall()
            
            # 检查索引使用统计
            result = await db.execute(text("""
                SELECT 
                    TABLE_NAME,
                    INDEX_NAME,
                    CARDINALITY,
                    SUB_PART,
                    PACKED,
                    NULLABLE,
                    INDEX_TYPE
                FROM information_schema.STATISTICS 
                WHERE TABLE_SCHEMA = 'almond_parser' 
                AND TABLE_NAME = 'documents'
                ORDER BY TABLE_NAME, SEQ_IN_INDEX
            """))
            index_stats = result.fetchall()
            
            return {
                "indexes": [dict(row._mapping) for row in indexes],
                "index_statistics": [dict(row._mapping) for row in index_stats]
            }
            
        except Exception as e:
            logger.error(f"检查索引失败: {e}")
            return {}
    
    @staticmethod
    async def optimize_tables(db: AsyncSession) -> Dict[str, Any]:
        """优化表"""
        try:
            results = {}
            
            # 优化documents表
            result = await db.execute(text("OPTIMIZE TABLE documents"))
            results["documents"] = dict(result.fetchone()._mapping)
            
            # 优化document_logs表
            result = await db.execute(text("OPTIMIZE TABLE document_logs"))
            results["document_logs"] = dict(result.fetchone()._mapping)
            
            # 优化batch_tasks表
            result = await db.execute(text("OPTIMIZE TABLE batch_tasks"))
            results["batch_tasks"] = dict(result.fetchone()._mapping)
            
            return results
            
        except Exception as e:
            logger.error(f"优化表失败: {e}")
            return {}
    
    @staticmethod
    async def get_connection_pool_status() -> Dict[str, Any]:
        """获取连接池状态"""
        from almond_parser.db.database import db_manager
        
        try:
            engine = db_manager.engine
            pool = engine.pool
            
            return {
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid()
            }
        except Exception as e:
            logger.error(f"获取连接池状态失败: {e}")
            return {}


async def run_performance_check(db: AsyncSession) -> Dict[str, Any]:
    """运行性能检查"""
    optimizer = DatabaseOptimizer()
    
    start_time = time.time()
    
    # 并发执行多个检查
    import asyncio
    
    tasks = [
        optimizer.analyze_slow_queries(db),
        optimizer.check_indexes(db),
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return {
        "database_analysis": results[0] if not isinstance(results[0], Exception) else str(results[0]),
        "index_analysis": results[1] if not isinstance(results[1], Exception) else str(results[1]),
        "connection_pool": optimizer.get_connection_pool_status(),
        "check_duration": time.time() - start_time
    }


# 推荐的数据库优化配置
RECOMMENDED_MYSQL_CONFIG = """
# MySQL 性能优化配置建议

[mysqld]
# 连接相关
max_connections = 200
max_connect_errors = 100000
connect_timeout = 10
wait_timeout = 28800
interactive_timeout = 28800

# 缓冲区设置
innodb_buffer_pool_size = 1G  # 设置为可用内存的70-80%
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2

# 查询缓存
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# 临时表
tmp_table_size = 64M
max_heap_table_size = 64M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 1

# 其他优化
innodb_file_per_table = 1
innodb_flush_method = O_DIRECT
"""
