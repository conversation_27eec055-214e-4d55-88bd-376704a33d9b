from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, func
from sqlalchemy.orm import relationship

from almond_parser.db.database import Base


class ApiKey(Base):
    __tablename__ = 'api_keys'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment='密钥名称')
    key = Column(String(100), unique=True, index=True, nullable=False, comment='API密钥')
    user_id = Column(Integer, nullable=False, comment='用户ID')
    is_enabled = Column(Boolean, default=True, comment='是否启用')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    last_used_at = Column(DateTime, nullable=True, comment='最后使用时间')
    expires_at = Column(DateTime, nullable=True, comment='过期时间')
    usage_count = Column(Integer, default=0, comment='使用次数')


    class Config:
        from_attributes = True
