# -*- encoding: utf-8 -*-
"""
文档管理数据模型
"""
import enum
from datetime import datetime
from typing import Optional

from sqlalchemy import String, DateTime, Enum as SQLEnum, Integer, Text, JSON, Boolean, func, Column, Index
from almond_parser.db.database import Base


class DocumentStatus(str, enum.Enum):
    """文档状态枚举"""
    UPLOADING = "UPLOADING"      # 上传中
    UPLOADED = "UPLOADED"        # 已上传
    PARSING = "PARSING"          # 解析中
    COMPLETED = "COMPLETED"      # 已完成
    FAILED = "FAILED"           # 失败
    RETRY_PENDING = "RETRY_PENDING"  # 等待重试
    FALLBACK_RETRY = "FALLBACK_RETRY"  # 降级重试中


class Document(Base):
    """文档信息表"""
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(String(100), unique=True, nullable=False, index=True, comment="文档ID")
    batch_id = Column(String(100), nullable=False, index=True, comment="批次ID")

    # 文件信息
    file_name = Column(String(255), nullable=False, comment="文件名")
    file_size = Column(Integer, nullable=True, comment="文件大小(字节)")
    file_type = Column(String(50), nullable=True, comment="文件类型")
    file_path = Column(String(500), nullable=True, comment="文件路径")

    # 用户和节点信息
    user_id = Column(String(255), nullable=False, index=True, comment="用户ID")
    node_id = Column(Integer, nullable=True, comment="处理节点ID")

    # 状态信息
    status = Column(SQLEnum(DocumentStatus), default=DocumentStatus.UPLOADED, index=True, comment="文档状态")
    progress = Column(Integer, default=0, comment="处理进度(0-100)")
    error_message = Column(Text, nullable=True, comment="错误信息")

    # 解析结果
    result_data = Column(JSON, nullable=True, comment="解析结果数据")
    output_path = Column(String(500), nullable=True, comment="输出文件路径")

    # 处理时间
    started_at = Column(DateTime, nullable=True, comment="开始处理时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")

    # 重试信息
    retry_count = Column(Integer, default=0, comment="重试次数")
    max_retries = Column(Integer, default=2, comment="最大重试次数")

    # 降级重试信息
    original_parse_mode = Column(String(50), nullable=True, comment="原始解析模式")
    current_parse_mode = Column(String(50), nullable=True, comment="当前解析模式")
    has_fallback = Column(Boolean, default=False, comment="是否已降级")

    # 重试类型标记
    retry_reason = Column(String(100), nullable=True, comment="重试原因")
    is_system_retry = Column(Boolean, default=False, comment="是否为系统重试")
    next_retry_at = Column(DateTime, nullable=True, comment="下次重试时间")

    # 解析任务id
    task_id = Column(String(100), nullable=True, comment="解析任务ID")

    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引
    __table_args__ = (
        Index('idx_batch_status', 'batch_id', 'status'),
        Index('idx_user_status', 'user_id', 'status'),
        Index('idx_created_at', 'created_at'),
    )

    def __repr__(self):
        return f"<Document(id={self.id}, document_id='{self.document_id}', status='{self.status}')>"


class DocumentLog(Base):
    """文档处理日志表"""
    __tablename__ = "document_logs"

    id = Column(Integer, primary_key=True)
    document_id = Column(String(100), nullable=False, index=True, comment="文档ID")

    # 日志信息
    level = Column(String(20), nullable=False, comment="日志级别")
    message = Column(Text, nullable=False, comment="日志消息")
    source = Column(String(100), nullable=True, comment="日志来源")

    # 额外数据
    extra_data = Column(JSON, nullable=True, comment="额外数据")

    # 时间戳
    created_at = Column(DateTime, default=func.now(), index=True, comment="创建时间")

    # 索引
    __table_args__ = (
        Index('idx_document_created', 'document_id', 'created_at'),
    )

    def __repr__(self):
        return f"<DocumentLog(id={self.id}, document_id='{self.document_id}', level='{self.level}')>"


class BatchTask(Base):
    """批次任务表"""
    __tablename__ = "batch_tasks"

    id = Column(Integer, primary_key=True)
    batch_id = Column(String(100), unique=True, nullable=False, index=True, comment="批次ID")

    # 任务信息
    user_id = Column(String(255), nullable=False, index=True, comment="用户ID")
    total_files = Column(Integer, nullable=False, comment="总文件数")
    completed_files = Column(Integer, default=0, comment="已完成文件数")
    failed_files = Column(Integer, default=0, comment="失败文件数")

    # 状态信息
    status = Column(String(50), default="processing", comment="批次状态")
    progress = Column(Integer, default=0, comment="整体进度(0-100)")

    # 配置信息
    priority = Column(Integer, default=1, comment="任务优先级")
    config_data = Column(JSON, nullable=True, comment="配置数据")

    # 时间信息
    estimated_time = Column(Integer, nullable=True, comment="预估完成时间(秒)")
    started_at = Column(DateTime, nullable=True, comment="开始时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")

    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<BatchTask(id={self.id}, batch_id='{self.batch_id}', status='{self.status}')>"
