# -*- encoding: utf-8 -*-
"""
数据库连接管理 - MySQL 连接池
"""
from typing import AsyncGenerator
import os
try:
    # SQLAlchemy 2.0+
    from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
except ImportError:
    # SQLAlchemy 1.4
    from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
    from sqlalchemy.orm import sessionmaker
    async_sessionmaker = sessionmaker

from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import NullPool
from loguru import logger
from sqlalchemy import text

from almond_parser.config import settings


class DatabaseManager:
    """数据库管理器 - 单例模式"""

    _instance = None
    _engine = None
    _session_factory = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if self._engine is None:
            self._initialize()

    def _initialize(self):
        """初始化数据库连接"""
        try:
            # 创建异步引擎 - 使用连接池而不是NullPool
            self._engine = create_async_engine(
                settings.database_url,
                # 移除 poolclass=NullPool，使用默认的 QueuePool
                pool_size=settings.DB_POOL_SIZE,  # 连接池大小
                max_overflow=settings.DB_MAX_OVERFLOW,  # 最大溢出连接数
                pool_timeout=settings.DB_POOL_TIMEOUT,  # 获取连接超时时间
                pool_pre_ping=True,  # 连接前检查
                pool_recycle=settings.DB_POOL_RECYCLE,  # 连接回收时间
                echo=False,  # 关闭SQL日志以提升性能
                # 异步引擎优化参数
                connect_args={
                    "charset": "utf8mb4",
                    "autocommit": False,
                    "connect_timeout": 10,  # 连接超时
                    # "read_timeout": 30,     # 读取超时
                    # "write_timeout": 30,    # 写入超时
                }
            )

            # 创建会话工厂
            self._session_factory = async_sessionmaker(
                bind=self._engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False,
            )

            logger.info("数据库连接池初始化成功")

        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise

    @property
    def engine(self):
        """获取数据库引擎"""
        return self._engine

    @property
    def session_factory(self):
        """获取会话工厂"""
        return self._session_factory

    async def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        if self._session_factory is None:
            raise RuntimeError("数据库未初始化")
        return self._session_factory()

    async def close(self):
        """关闭数据库连接"""
        if self._engine:
            await self._engine.dispose()
            logger.info("数据库连接池已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()

# 数据库基类
Base = declarative_base()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话依赖"""
    async with db_manager.session_factory() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话异常: {e}")
            raise
        finally:
            await session.close()


def get_async_session():
    """获取异步数据库会话上下文管理器"""
    return db_manager.session_factory()


async def init_database():
    """初始化数据库表"""
    # 检查是否需要初始化
    if os.getenv("ALMOND_PARSER_INIT_DB", "").lower() != "true":
        logger.info("跳过数据库初始化 (ALMOND_PARSER_INIT_DB != true)")
        return

    try:
        # 创建一个临时引擎，不指定数据库
        temp_url = f"mysql+aiomysql://{settings.MYSQL_USER}:{settings.MYSQL_PASSWORD}@{settings.MYSQL_HOST}:{settings.MYSQL_PORT}/"
        temp_engine = create_async_engine(
            temp_url,
            isolation_level="AUTOCOMMIT",  # 需要 AUTOCOMMIT 来创建数据库
        )

        # 创建数据库
        async with temp_engine.connect() as conn:
            await conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {settings.MYSQL_DATABASE} CHARACTER SET {settings.MYSQL_CHARSET}"))
            logger.info(f"确保数据库 {settings.MYSQL_DATABASE} 存在")

        # 关闭临时引擎
        await temp_engine.dispose()

        # 使用正常的连接创建表
        async with db_manager.engine.begin() as conn:
            # 导入所有模型以确保表被创建
            from almond_parser.db.models import api_key, mineru_node, user, document

            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
            logger.info("数据库表初始化完成")

    except Exception as e:
        logger.error(f"数据库表初始化失败: {e}")
        raise


async def close_database():
    """关闭数据库连接"""
    await db_manager.close()
