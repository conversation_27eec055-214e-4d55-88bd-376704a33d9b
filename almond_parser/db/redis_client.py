# -*- encoding: utf-8 -*-
"""
Redis 连接管理 - 连接池
"""
import json
from typing import Any, Optional, Union
import aioredis
from loguru import logger

from almond_parser.config import settings


class RedisManager:
    """Redis 管理器 - 单例模式"""
    
    _instance = None
    _redis_pool = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._redis_pool is None:
            self._initialize()
    
    def _initialize(self):
        """初始化Redis连接池"""
        try:
            # 创建Redis连接池
            self._redis_pool = aioredis.ConnectionPool.from_url(
                settings.redis_url,
                max_connections=settings.REDIS_POOL_SIZE,
                retry_on_timeout=True,
                health_check_interval=30,
            )
            
            logger.info("Redis连接池初始化成功")
            
        except Exception as e:
            logger.error(f"Redis连接池初始化失败: {e}")
            raise
    
    @property
    def pool(self):
        """获取Redis连接池"""
        return self._redis_pool
    
    async def get_redis(self) -> aioredis.Redis:
        """获取Redis连接"""
        if self._redis_pool is None:
            raise RuntimeError("Redis未初始化")
        return aioredis.Redis(connection_pool=self._redis_pool)
    
    async def close(self):
        """关闭Redis连接池"""
        if self._redis_pool:
            await self._redis_pool.disconnect()
            logger.info("Redis连接池已关闭")


class RedisClient:
    """Redis客户端封装"""
    
    def __init__(self):
        self.manager = RedisManager()
    
    async def get(self, key: str) -> Optional[str]:
        """获取值"""
        try:
            redis = await self.manager.get_redis()
            value = await redis.get(key)
            return value.decode('utf-8') if value else None
        except Exception as e:
            logger.error(f"Redis GET 操作失败 {key}: {e}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Union[str, dict, list], 
        expire: Optional[int] = None
    ) -> bool:
        """设置值"""
        try:
            redis = await self.manager.get_redis()
            
            # 如果是字典或列表，序列化为JSON
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            
            result = await redis.set(key, value, ex=expire)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis SET 操作失败 {key}: {e}")
            return False
    
    async def get_json(self, key: str) -> Optional[Union[dict, list]]:
        """获取JSON值"""
        try:
            value = await self.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"Redis GET JSON 操作失败 {key}: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除键"""
        try:
            redis = await self.manager.get_redis()
            result = await redis.delete(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis DELETE 操作失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            redis = await self.manager.get_redis()
            result = await redis.exists(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis EXISTS 操作失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置过期时间"""
        try:
            redis = await self.manager.get_redis()
            result = await redis.expire(key, seconds)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis EXPIRE 操作失败 {key}: {e}")
            return False
    
    async def incr(self, key: str, amount: int = 1) -> Optional[int]:
        """递增"""
        try:
            redis = await self.manager.get_redis()
            result = await redis.incr(key, amount)
            return result
        except Exception as e:
            logger.error(f"Redis INCR 操作失败 {key}: {e}")
            return None
    
    async def hset(self, name: str, key: str, value: Any) -> bool:
        """哈希表设置"""
        try:
            redis = await self.manager.get_redis()
            if isinstance(value, (dict, list)):
                value = json.dumps(value, ensure_ascii=False)
            result = await redis.hset(name, key, value)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis HSET 操作失败 {name}.{key}: {e}")
            return False
    
    async def hget(self, name: str, key: str) -> Optional[str]:
        """哈希表获取"""
        try:
            redis = await self.manager.get_redis()
            value = await redis.hget(name, key)
            return value.decode('utf-8') if value else None
        except Exception as e:
            logger.error(f"Redis HGET 操作失败 {name}.{key}: {e}")
            return None
    
    async def hgetall(self, name: str) -> dict:
        """获取哈希表所有值"""
        try:
            redis = await self.manager.get_redis()
            result = await redis.hgetall(name)
            return {k.decode('utf-8'): v.decode('utf-8') for k, v in result.items()}
        except Exception as e:
            logger.error(f"Redis HGETALL 操作失败 {name}: {e}")
            return {}


# 全局Redis管理器和客户端实例
redis_manager = RedisManager()
redis_client = RedisClient()


async def get_redis() -> RedisClient:
    """获取Redis客户端依赖"""
    return redis_client


async def close_redis():
    """关闭Redis连接"""
    await redis_manager.close()
