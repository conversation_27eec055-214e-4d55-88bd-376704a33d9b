#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
数据库迁移：为 NodeHealthCheck 表添加新字段
"""

import asyncio
from sqlalchemy import text
from loguru import logger

from almond_parser.db import db_manager


async def add_health_check_fields():
    """为 NodeHealthCheck 表添加新字段"""
    logger.info("开始为 NodeHealthCheck 表添加新字段...")
    
    try:
        async with db_manager.session_factory() as db:
            # 检查表是否存在
            result = await db.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='node_health_checks'
            """))
            
            if not result.fetchone():
                logger.warning("node_health_checks 表不存在，跳过迁移")
                return
            
            # 检查字段是否已存在
            result = await db.execute(text("PRAGMA table_info(node_health_checks)"))
            columns = [row[1] for row in result.fetchall()]
            
            # 需要添加的字段
            new_fields = [
                ("server_type", "VARCHAR(50)"),
                ("sglang_available", "BOOLEAN"),
                ("detected_parse_mode", "VARCHAR(50)"),
                ("raw_response", "TEXT")
            ]
            
            # 添加缺失的字段
            for field_name, field_type in new_fields:
                if field_name not in columns:
                    logger.info(f"添加字段: {field_name} ({field_type})")
                    await db.execute(text(f"""
                        ALTER TABLE node_health_checks 
                        ADD COLUMN {field_name} {field_type}
                    """))
                else:
                    logger.info(f"字段 {field_name} 已存在，跳过")
            
            await db.commit()
            logger.success("✅ NodeHealthCheck 表字段添加完成")
            
    except Exception as e:
        logger.error(f"❌ 迁移失败: {e}")
        raise


async def verify_migration():
    """验证迁移结果"""
    logger.info("验证迁移结果...")
    
    try:
        async with db_manager.session_factory() as db:
            # 获取表结构
            result = await db.execute(text("PRAGMA table_info(node_health_checks)"))
            columns = result.fetchall()
            
            logger.info("当前表结构:")
            for col in columns:
                logger.info(f"  - {col[1]} ({col[2]})")
            
            # 检查必要字段
            column_names = [col[1] for col in columns]
            required_fields = [
                "server_type", "sglang_available", 
                "detected_parse_mode", "raw_response"
            ]
            
            missing_fields = [field for field in required_fields if field not in column_names]
            
            if missing_fields:
                logger.error(f"❌ 缺少字段: {missing_fields}")
                return False
            else:
                logger.success("✅ 所有必要字段都已存在")
                return True
                
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("🔄 开始数据库迁移...")
    
    try:
        # 初始化数据库连接
        await db_manager.init()
        
        # 执行迁移
        await add_health_check_fields()
        
        # 验证迁移
        success = await verify_migration()
        
        if success:
            logger.success("🎉 数据库迁移完成")
        else:
            logger.error("❌ 数据库迁移验证失败")
            
    except Exception as e:
        logger.error(f"❌ 迁移过程出错: {e}")
    finally:
        await db_manager.close()


if __name__ == "__main__":
    asyncio.run(main())
