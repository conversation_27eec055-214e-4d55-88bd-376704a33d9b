# -*- encoding: utf-8 -*-
"""
ARQ 异步任务队列配置
"""
import asyncio
from typing import Dict, Any
from arq import create_pool, ArqRedis, cron
from arq.connections import RedisSettings
from loguru import logger

from almond_parser.config import settings
from almond_parser.tasks import document_tasks, node_tasks, enhanced_document_tasks, retry_tasks, task_sync_service


class ArqManager:
    """ARQ 管理器 - 单例模式"""

    _instance = None
    _redis_pool: ArqRedis = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def initialize(self):
        """初始化 ARQ Redis 连接池"""
        if self._initialized:
            return

        if self._redis_pool is None:
            try:
                redis_settings = RedisSettings(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    password=settings.REDIS_PASSWORD,
                    database=1,  # 使用数据库1作为任务队列
                )

                self._redis_pool = await create_pool(redis_settings)
                self._initialized = True
                logger.info("✅ ARQ Redis 连接池初始化成功")

            except Exception as e:
                logger.error(f"❌ ARQ Redis 连接池初始化失败: {e}")
                raise RuntimeError(f"ARQ Redis 连接池初始化失败: {e}")

    @property
    def redis_pool(self) -> ArqRedis:
        """获取 Redis 连接池"""
        if not self._initialized or self._redis_pool is None:
            raise RuntimeError("ARQ Redis 连接池未初始化，请先调用 initialize()")
        return self._redis_pool

    async def enqueue_task(
            self,
            task_name: str,
            *args,
            **kwargs
    ) -> str:
        """入队任务"""
        if not self._initialized:
            await self.initialize()

        try:
            job = await self.redis_pool.enqueue_job(task_name, *args, **kwargs)
            logger.info(f"✅ 任务入队成功: {task_name}, job_id: {job.job_id}")
            return job.job_id
        except Exception as e:
            error_msg = f"❌ 任务入队失败: {task_name}, 错误: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

    async def get_job_result(self, job_id: str) -> Dict[str, Any]:
        """获取任务结果"""
        try:
            # 使用 redis_pool 属性确保连接池已初始化
            job = await self.redis_pool.get_job(job_id)
            if job is None:
                return {"status": "not_found", "result": None}

            if job.finished:
                return {
                    "status": "completed",
                    "result": job.result,
                    "success": job.success
                }
            elif job.in_progress:
                return {"status": "running", "result": None}
            else:
                return {"status": "pending", "result": None}

        except Exception as e:
            logger.error(f"获取任务结果失败: {job_id}, 错误: {e}")
            return {"status": "error", "result": str(e)}

    async def close(self):
        """关闭连接池"""
        if self._redis_pool:
            await self._redis_pool.close()
            logger.info("ARQ Redis 连接池已关闭")


# 全局 ARQ 管理器实例
arq_manager = ArqManager()


async def get_arq_redis() -> ArqRedis:
    """获取 ARQ Redis 连接"""
    return arq_manager.redis_pool


async def startup_arq():
    """启动 ARQ"""
    await arq_manager.initialize()


async def shutdown_arq():
    """关闭 ARQ"""
    await arq_manager.close()


# 工作器启动和关闭钩子
async def worker_startup(ctx):
    """工作器启动时的初始化"""
    from almond_parser.db.database import init_database
    logger.info("🔧 初始化工作器数据库...")
    await init_database()
    logger.info("✅ 工作器数据库初始化完成")


async def worker_shutdown(ctx):
    """工作器关闭时的清理"""
    from almond_parser.db.database import close_database
    logger.info("🔧 关闭工作器数据库连接...")
    await close_database()
    logger.info("✅ 工作器数据库连接已关闭")


# ARQ 工作器配置
class WorkerSettings:
    """ARQ 工作器配置"""

    redis_settings = RedisSettings(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        password=settings.REDIS_PASSWORD,
        database=1,
    )

    # 任务函数：直接传函数对象，而不是字符串路径
    functions = [
        document_tasks.process_document,
        document_tasks.process_batch_documents,
        document_tasks.retry_document,
        document_tasks.query_document_status,
        node_tasks.health_check_node,
        node_tasks.health_check_all_nodes,
        # 增强的文档处理任务
        enhanced_document_tasks.enhanced_process_document,
        enhanced_document_tasks.enhanced_process_document_result,
        # 重试任务
        retry_tasks.process_retry_documents,
        retry_tasks.cleanup_old_retry_records,
    ]

    # 工作器生命周期钩子
    on_startup = worker_startup
    on_shutdown = worker_shutdown

    max_jobs = 10
    job_timeout = 7200  # 任务超时时间：2小时
    keep_result = 86400  # 任务结果保留时间：1天

    # 定时任务配置
    # ARQ cron 支持的时间单位：minute, hour, day, weekday, month
    # minute=None 表示每分钟执行，minute=1 表示每小时的第1分钟执行
    cron_jobs = [
        cron(
            coroutine=node_tasks.health_check_all_nodes,
            second=30,
            run_at_startup=True,
        ),
        # 每5分钟检查重试文档
        cron(
            coroutine=retry_tasks.process_retry_documents,
            minute={0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55},
            run_at_startup=False,
        ),
        # 每小时清理过期重试记录
        cron(
            coroutine=retry_tasks.cleanup_old_retry_records,
            minute=0,
            run_at_startup=False,
        ),
        # 每10分钟同步卡住的任务状态
        cron(
            coroutine=task_sync_service.sync_stuck_tasks,
            minute={0, 10, 20, 30, 40, 50},
            # minute=1,
            # second=59,
            run_at_startup=False,
        )
    ]

    log_results = True
    max_tries = 3
    retry_delay = 60
