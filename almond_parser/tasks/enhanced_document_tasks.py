# -*- encoding: utf-8 -*-
"""
增强的文档处理任务 - 支持资源保护型节点分配和智能降级重试
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from urllib.parse import urljoin
import base64

from loguru import logger
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from almond_parser.db import db_manager
from almond_parser.db.models.document import Document, DocumentStatus, DocumentLog
from almond_parser.db.models.mineru_node import MinerUNode, NodeStatus
from almond_parser.services.document_service import DocumentService
from almond_parser.utils.node_selector import NodeSelector
from almond_parser.config import settings


async def enhanced_process_document(
        ctx: Dict[str, Any],
        document_id: str,
        user_id: str,
        service_type: str = "auto",
        parse_mode: str = "auto",
        max_retries: int = 2,
        config: Optional[Dict[str, Any]] = None,
        is_fallback_retry: bool = False
) -> Dict[str, Any]:
    """
    增强的文档处理任务 - 支持资源保护型节点分配和智能降级重试

    Args:
        ctx: ARQ 上下文
        document_id: 文档ID
        user_id: 用户ID
        service_type: 服务类型
        parse_mode: 解析模式
        max_retries: 最大重试次数
        config: 解析配置
        is_fallback_retry: 是否为降级重试
    """
    logger.info(f"开始处理文档: {document_id}, 模式: {parse_mode}, 降级重试: {is_fallback_retry}")

    async with db_manager.session_factory() as db:
        try:
            # 获取文档信息
            doc_service = DocumentService(db)
            document = await doc_service.get_document(document_id)

            if not document:
                raise ValueError(f"文档 {document_id} 不存在")

            # 记录原始解析模式
            if not document.original_parse_mode:
                document.original_parse_mode = parse_mode
                document.current_parse_mode = parse_mode

            # 如果是降级重试，更新解析模式
            if is_fallback_retry:
                document.current_parse_mode = "pipeline"
                document.has_fallback = True
                document.status = DocumentStatus.FALLBACK_RETRY
                await _log_document_event(
                    db, document_id, "INFO",
                    f"开始降级重试: {document.original_parse_mode} -> pipeline"
                )

            # 选择可用节点（使用资源保护型兼容性匹配）
            node_selector = NodeSelector(db)
            node = await node_selector.select_node(
                service_type=service_type,
                parse_mode=document.current_parse_mode,
                use_compatibility=True
            )

            if not node:
                # 节点不可用，标记为系统重试
                await _handle_node_unavailable(db, document, service_type, parse_mode)
                return {
                    "success": False,
                    "document_id": document_id,
                    "error": "no_available_nodes",
                    "retry_scheduled": True
                }

            # 更新文档状态
            document.node_id = node.id
            document.status = DocumentStatus.PARSING
            document.progress = 10
            document.started_at = datetime.utcnow()
            await db.commit()

            # 获取实际执行模式
            execution_mode = getattr(node, '_execution_mode', document.current_parse_mode)
            
            # 记录节点分配详情
            await _log_document_event(
                db, document_id, "INFO",
                f"节点分配成功: {node.name} (节点模式: {node.parse_mode.value}, 执行模式: {execution_mode})",
                {
                    "node_id": node.id,
                    "node_name": node.name,
                    "node_mode": node.parse_mode.value,
                    "execution_mode": execution_mode,
                    "service_type": node.service_type.value,
                    "resource_protection": node.parse_mode.value == "pipeline" and document.current_parse_mode == "pipeline"
                }
            )

            # 调用 MinerU API
            api_result = await _call_mineru_api_enhanced(
                node, document, config, execution_mode
            )

            if api_result["success"]:
                document.task_id = api_result["task_id"]
                await db.commit()

                await _log_document_event(
                    db, document_id, "INFO",
                    f"MinerU API 调用成功: {api_result.get('message', '任务已提交')}"
                )

                return {
                    "success": True,
                    "document_id": document_id,
                    "status": "submitted_to_mineru",
                    "node": node.name,
                    "task_id": api_result.get("task_id"),
                    "parse_mode": document.current_parse_mode,
                    "execution_mode": execution_mode
                }
            else:
                # API 调用失败，判断是否需要降级重试
                await _handle_api_failure(
                    db, document, api_result["error"],
                    user_id, service_type, config
                )

                return {
                    "success": False,
                    "document_id": document_id,
                    "error": api_result["error"],
                    "fallback_scheduled": not is_fallback_retry and document.original_parse_mode != "pipeline"
                }

        except Exception as e:
            logger.error(f"文档处理异常: {document_id}, 错误: {e}")

            # 更新文档状态为失败
            try:
                document.status = DocumentStatus.FAILED
                document.error_message = str(e)
                await db.commit()

                await _log_document_event(
                    db, document_id, "ERROR",
                    f"文档处理异常: {str(e)}"
                )
            except Exception as commit_error:
                logger.error(f"更新文档状态失败: {commit_error}")

            return {
                "success": False,
                "document_id": document_id,
                "error": str(e)
            }


async def enhanced_process_document_result(
        ctx: Dict[str, Any],
        document_id: str,
        result_data: Dict[str, Any],
        task_id: str,
        success: bool = True
) -> Dict[str, Any]:
    """
    增强的文档结果处理 - 支持结果检查和自动降级重试
    
    Args:
        ctx: ARQ 上下文
        document_id: 文档ID
        result_data: 解析结果数据
        task_id: 任务ID
        success: 是否成功
    """
    logger.info(f"处理文档结果: {document_id}, 任务: {task_id}, 成功: {success}")
    
    async with db_manager.session_factory() as db:
        try:
            # 获取文档信息
            doc_service = DocumentService(db)
            document = await doc_service.get_document(document_id)
            
            if not document:
                raise ValueError(f"文档 {document_id} 不存在")
            
            if success and result_data:
                # 检查解析结果质量
                quality_check = await _check_result_quality(result_data)
                
                if quality_check["is_valid"]:
                    # 结果有效，保存成功结果
                    document.status = DocumentStatus.COMPLETED
                    document.result_data = result_data
                    document.completed_at = datetime.utcnow()
                    document.progress = 100
                    
                    await _log_document_event(
                        db, document_id, "INFO",
                        f"解析完成: {quality_check['summary']}"
                    )
                    
                    await db.commit()
                    
                    return {
                        "success": True,
                        "document_id": document_id,
                        "status": "completed",
                        "quality_check": quality_check
                    }
                else:
                    # 结果质量不佳，考虑降级重试
                    return await _handle_poor_quality_result(
                        db, document, quality_check, result_data
                    )
            else:
                # 解析失败，考虑降级重试
                return await _handle_parsing_failure(
                    db, document, result_data.get("error", "解析失败")
                )
                
        except Exception as e:
            logger.error(f"处理文档结果异常: {document_id}, 错误: {e}")
            
            try:
                document.status = DocumentStatus.FAILED
                document.error_message = str(e)
                await db.commit()
                
                await _log_document_event(
                    db, document_id, "ERROR",
                    f"结果处理异常: {str(e)}"
                )
            except Exception as commit_error:
                logger.error(f"更新文档状态失败: {commit_error}")
            
            return {
                "success": False,
                "document_id": document_id,
                "error": str(e)
            }


async def _handle_node_unavailable(
        db: AsyncSession,
        document: Document,
        service_type: str,
        parse_mode: str
) -> None:
    """处理节点不可用情况 - 增强版"""
    document.status = DocumentStatus.RETRY_PENDING
    document.is_system_retry = True
    document.retry_reason = "node_unavailable"
    document.next_retry_at = datetime.utcnow() + timedelta(minutes=5)  # 5分钟后重试

    await db.commit()

    await _log_document_event(
        db, document.document_id, "WARNING",
        f"节点不可用，已安排系统重试: {document.next_retry_at}",
        {
            "service_type": service_type,
            "parse_mode": parse_mode,
            "retry_reason": "node_unavailable"
        }
    )


async def _handle_api_failure(
        db: AsyncSession,
        document: Document,
        error_message: str,
        user_id: str,
        service_type: str,
        config: Optional[Dict[str, Any]]
) -> None:
    """处理API调用失败 - 增强版"""
    # 判断是否为VLM模式且未进行过降级重试
    can_fallback = (
        document.original_parse_mode in ["sglang", "vlm", "vlm-sglang-client"] and
        not document.has_fallback and
        document.current_parse_mode != "pipeline"
    )

    if can_fallback:
        # 安排降级重试
        logger.info(f"安排降级重试: {document.document_id}")

        # 延迟导入避免循环依赖
        from almond_parser.tasks.arq_app import arq_manager

        job_id = await arq_manager.enqueue_task(
            "enhanced_process_document",
            document_id=document.document_id,
            user_id=user_id,
            service_type=service_type,
            parse_mode="pipeline",
            config=config,
            is_fallback_retry=True
        )

        await _log_document_event(
            db, document.document_id, "INFO",
            f"VLM解析失败，已安排降级重试任务: {job_id}",
            {
                "original_mode": document.original_parse_mode,
                "fallback_mode": "pipeline",
                "job_id": job_id,
                "error": error_message
            }
        )
    else:
        # 标记为失败
        document.status = DocumentStatus.FAILED
        document.error_message = error_message
        await db.commit()

        await _log_document_event(
            db, document.document_id, "ERROR",
            f"解析失败: {error_message}",
            {"cannot_fallback": True, "reason": "已经是pipeline模式或已尝试降级"}
        )


async def _handle_poor_quality_result(
        db: AsyncSession,
        document: Document,
        quality_check: Dict[str, Any],
        result_data: Dict[str, Any]
) -> Dict[str, Any]:
    """处理质量不佳的解析结果"""
    # 判断是否可以降级重试
    can_fallback = (
        document.original_parse_mode in ["sglang", "vlm", "vlm-sglang-client"] and
        not document.has_fallback and
        document.current_parse_mode != "pipeline"
    )

    if can_fallback:
        # 安排降级重试
        logger.info(f"解析结果质量不佳，安排降级重试: {document.document_id}")

        # 延迟导入避免循环依赖
        from almond_parser.tasks.arq_app import arq_manager

        job_id = await arq_manager.enqueue_task(
            "enhanced_process_document",
            document_id=document.document_id,
            user_id=document.user_id,
            service_type="auto",
            parse_mode="pipeline",
            config={},
            is_fallback_retry=True
        )

        await _log_document_event(
            db, document.document_id, "WARNING",
            f"解析结果质量不佳，已安排降级重试: {job_id}",
            {
                "quality_issues": quality_check["issues"],
                "original_mode": document.original_parse_mode,
                "fallback_mode": "pipeline",
                "job_id": job_id
            }
        )

        return {
            "success": False,
            "document_id": document.document_id,
            "error": "poor_quality_result",
            "fallback_scheduled": True,
            "quality_check": quality_check
        }
    else:
        # 无法降级，接受当前结果
        document.status = DocumentStatus.COMPLETED
        document.result_data = result_data
        document.completed_at = datetime.utcnow()
        document.progress = 100

        await _log_document_event(
            db, document.document_id, "WARNING",
            f"解析结果质量不佳但无法降级，接受当前结果: {quality_check['summary']}",
            {"quality_issues": quality_check["issues"]}
        )

        await db.commit()

        return {
            "success": True,
            "document_id": document.document_id,
            "status": "completed_with_issues",
            "quality_check": quality_check
        }


async def _handle_parsing_failure(
        db: AsyncSession,
        document: Document,
        error_message: str
) -> Dict[str, Any]:
    """处理解析失败"""
    # 判断是否可以降级重试
    can_fallback = (
        document.original_parse_mode in ["sglang", "vlm", "vlm-sglang-client"] and
        not document.has_fallback and
        document.current_parse_mode != "pipeline"
    )

    if can_fallback:
        # 安排降级重试
        logger.info(f"解析失败，安排降级重试: {document.document_id}")

        # 延迟导入避免循环依赖
        from almond_parser.tasks.arq_app import arq_manager

        job_id = await arq_manager.enqueue_task(
            "enhanced_process_document",
            document_id=document.document_id,
            user_id=document.user_id,
            service_type="auto",
            parse_mode="pipeline",
            config={},
            is_fallback_retry=True
        )

        await _log_document_event(
            db, document.document_id, "INFO",
            f"解析失败，已安排降级重试: {job_id}",
            {
                "original_mode": document.original_parse_mode,
                "fallback_mode": "pipeline",
                "job_id": job_id,
                "error": error_message
            }
        )

        return {
            "success": False,
            "document_id": document.document_id,
            "error": error_message,
            "fallback_scheduled": True
        }
    else:
        # 标记为失败
        document.status = DocumentStatus.FAILED
        document.error_message = error_message
        await db.commit()

        await _log_document_event(
            db, document.document_id, "ERROR",
            f"解析失败且无法降级: {error_message}",
            {"cannot_fallback": True}
        )

        return {
            "success": False,
            "document_id": document.document_id,
            "error": error_message,
            "fallback_scheduled": False
        }


async def _check_result_quality(result_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    检查解析结果质量

    Args:
        result_data: 解析结果数据

    Returns:
        质量检查结果
    """
    try:
        issues = []

        # 检查是否有内容
        content = result_data.get("content", "")
        if not content or len(content.strip()) < 10:
            issues.append("内容过短或为空")

        # 检查是否有结构化数据
        markdown = result_data.get("markdown", "")
        if not markdown or len(markdown.strip()) < 10:
            issues.append("Markdown内容过短或为空")

        # 检查是否有页面信息
        pages = result_data.get("pages", [])
        if not pages:
            issues.append("缺少页面信息")

        # 检查是否有异常错误信息
        error_indicators = ["error", "failed", "exception", "timeout"]
        content_lower = content.lower() if content else ""
        for indicator in error_indicators:
            if indicator in content_lower:
                issues.append(f"内容中包含错误指示词: {indicator}")

        # 综合评估
        is_valid = len(issues) == 0

        # 生成摘要
        if is_valid:
            summary = f"解析成功，内容长度: {len(content)}, 页面数: {len(pages)}"
        else:
            summary = f"解析质量不佳，发现 {len(issues)} 个问题"

        return {
            "is_valid": is_valid,
            "issues": issues,
            "summary": summary,
            "content_length": len(content) if content else 0,
            "page_count": len(pages) if pages else 0
        }

    except Exception as e:
        logger.error(f"质量检查异常: {e}")
        return {
            "is_valid": False,
            "issues": [f"质量检查异常: {str(e)}"],
            "summary": "质量检查失败",
            "content_length": 0,
            "page_count": 0
        }


async def _call_mineru_api_enhanced(
        node: MinerUNode,
        document: Document,
        config: Optional[Dict[str, Any]],
        parse_mode: str
) -> Dict[str, Any]:
    """增强的 MinerU API 调用 - 支持资源保护策略"""
    try:
        import aiohttp

        # 构建请求数据
        parse_url = urljoin(node.base_url, "/predict")
        callback_url = f"{settings.HOST_URL}/api/v1/document/callback"

        # 读取文件并编码
        with open(document.file_path, "rb") as f:
            file_bytes = f.read()
        file_b64 = base64.b64encode(file_bytes).decode("utf-8")

        # 构建请求数据，确保使用正确的解析模式
        request_data = {
            "document_id": document.document_id,
            "file_name": document.file_name,
            "file_content": file_b64,
            "callback_url": callback_url,
            "callback_headers": {"Authorization": f"Bearer {node.auth_token}"},
            "backend": parse_mode,  # 使用当前解析模式
            **(config or {})
        }

        logger.info(f"调用MinerU API: {parse_url}, 模式: {parse_mode}, 节点: {node.name}")

        # 发送请求
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(
                parse_url,
                json=request_data,
                headers={"Authorization": f"Bearer {node.auth_token}"}
            ) as response:

                if response.status == 200:
                    result = await response.json()
                    logger.info(f"MinerU API 调用成功: {node.name}, 任务ID: {result.get('task_id')}")
                    return {
                        "success": True,
                        "task_id": result.get("task_id"),
                        "message": result.get("message", "任务提交成功")
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"MinerU API 调用失败: {node.name}, HTTP {response.status}: {error_text}")
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}"
                    }

    except Exception as e:
        logger.error(f"调用 MinerU API 异常: {node.name}, 错误: {e}")
        return {
            "success": False,
            "error": f"API调用异常: {str(e)}"
        }


async def _log_document_event(
        db: AsyncSession,
        document_id: str,
        level: str,
        message: str,
        extra_data: Optional[Dict[str, Any]] = None
) -> None:
    """记录文档事件日志"""
    try:
        log_entry = DocumentLog(
            document_id=document_id,
            level=level,
            message=message,
            source="enhanced_document_tasks",
            extra_data=extra_data
        )
        db.add(log_entry)
        await db.commit()
    except Exception as e:
        logger.error(f"记录文档日志失败: {e}")
