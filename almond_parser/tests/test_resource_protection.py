#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试资源保护型节点分配策略
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from almond_parser.db.database import get_db
from almond_parser.utils.node_selector import NodeSelector
from almond_parser.db.models import ServiceType


async def test_resource_protection_strategy():
    """测试资源保护策略"""
    print("🧪 开始测试资源保护型节点分配策略...")
    
    try:
        async for db in get_db():
            selector = NodeSelector(db)
            
            # 测试场景1：pipe请求应该优先分配给pipeline节点
            print("\n📋 测试场景1: pipe请求的节点分配")
            test_cases = [
                ("document", "pipeline"),
                ("document", "pipe"),
                ("knowledge_base", "pipeline"),
                ("universal", "pipeline"),
            ]
            
            for service_type, parse_mode in test_cases:
                print(f"\n  测试: 服务类型={service_type}, 解析模式={parse_mode}")
                
                node = await selector.select_node(
                    service_type=service_type,
                    parse_mode=parse_mode,
                    use_compatibility=True
                )
                
                if node:
                    execution_mode = getattr(node, '_execution_mode', parse_mode)
                    print(f"    ✅ 选中节点: {node.name}")
                    print(f"       节点模式: {node.parse_mode.value}")
                    print(f"       执行模式: {execution_mode}")
                    print(f"       服务类型: {node.service_type.value}")
                    print(f"       当前任务: {node.current_tasks}/{node.max_concurrent_tasks}")
                    
                    # 验证资源保护策略
                    if node.parse_mode.value == "pipeline":
                        print(f"    🎯 资源保护成功: 使用了纯pipeline节点")
                    elif node.parse_mode.value in ["sglang", "vlm"]:
                        print(f"    ⚠️  降级使用: 使用了sglang节点（无可用pipeline节点）")
                else:
                    print(f"    ❌ 未找到可用节点")
            
            # 测试场景2：vlm/sglang请求应该直接分配给sglang节点
            print("\n📋 测试场景2: vlm/sglang请求的节点分配")
            test_cases = [
                ("document", "sglang"),
                ("document", "vlm"),
                ("knowledge_base", "sglang"),
                ("universal", "vlm"),
            ]
            
            for service_type, parse_mode in test_cases:
                print(f"\n  测试: 服务类型={service_type}, 解析模式={parse_mode}")
                
                node = await selector.select_node(
                    service_type=service_type,
                    parse_mode=parse_mode,
                    use_compatibility=True
                )
                
                if node:
                    execution_mode = getattr(node, '_execution_mode', parse_mode)
                    print(f"    ✅ 选中节点: {node.name}")
                    print(f"       节点模式: {node.parse_mode.value}")
                    print(f"       执行模式: {execution_mode}")
                    print(f"       服务类型: {node.service_type.value}")
                    print(f"       当前任务: {node.current_tasks}/{node.max_concurrent_tasks}")
                    
                    # 验证VLM节点选择
                    if node.parse_mode.value in ["sglang", "vlm"]:
                        print(f"    🎯 VLM节点选择成功")
                    else:
                        print(f"    ⚠️  意外的节点类型: {node.parse_mode.value}")
                else:
                    print(f"    ❌ 未找到可用节点")
            
            # 测试场景3：auto模式的处理
            print("\n📋 测试场景3: auto模式的节点分配")
            node = await selector.select_node(
                service_type="document",
                parse_mode="auto",
                use_compatibility=True
            )
            
            if node:
                execution_mode = getattr(node, '_execution_mode', "auto")
                print(f"    ✅ 选中节点: {node.name}")
                print(f"       节点模式: {node.parse_mode.value}")
                print(f"       执行模式: {execution_mode}")
                print(f"       服务类型: {node.service_type.value}")
            else:
                print(f"    ❌ 未找到可用节点")
            
            break
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_node_availability():
    """测试节点可用性"""
    print("\n🔍 检查节点可用性...")
    
    try:
        async for db in get_db():
            selector = NodeSelector(db)
            
            # 获取服务类型统计
            stats = await selector.get_service_type_stats()
            print("\n📊 节点统计:")
            for service_type, data in stats.items():
                print(f"  {service_type}: 总计 {data['total']}, 在线 {data['online']}, 离线 {data['offline']}")
            
            # 获取各类型节点列表
            print("\n📋 可用节点列表:")
            for service_type in ["document", "knowledge_base"]:
                nodes = await selector.get_available_nodes(service_type, include_universal=True)
                print(f"\n  {service_type} 类型节点:")
                for node in nodes:
                    print(f"    - {node.name} (模式: {node.parse_mode.value}, 类型: {node.service_type.value}, 任务: {node.current_tasks}/{node.max_concurrent_tasks})")
            
            break
            
    except Exception as e:
        print(f"❌ 节点可用性检查失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    await test_node_availability()
    await test_resource_protection_strategy()
    print("\n✅ 测试完成")


if __name__ == "__main__":
    asyncio.run(main())
