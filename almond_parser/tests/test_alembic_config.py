#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试 Alembic 配置
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_alembic_imports():
    """测试 Alembic 相关导入"""
    print("🧪 测试 Alembic 导入...")
    
    try:
        import alembic
        print(f"  ✅ Alembic 版本: {alembic.__version__}")
        
        from alembic import context
        print("  ✅ Alembic context 导入成功")
        
        from alembic.config import Config
        print("  ✅ Alembic Config 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Alembic 导入失败: {e}")
        print("  💡 请安装 alembic: pip install alembic")
        return False


def test_project_imports():
    """测试项目模块导入"""
    print("\n🔧 测试项目模块导入...")
    
    try:
        from almond_parser.config import settings
        print("  ✅ 配置模块导入成功")
        
        from almond_parser.db.database import Base
        print("  ✅ 数据库基类导入成功")
        
        # 测试模型导入
        from almond_parser.db.models import api_key, document, mineru_node, user
        print("  ✅ 数据库模型导入成功")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 项目模块导入失败: {e}")
        return False


def test_database_config():
    """测试数据库配置"""
    print("\n🗄️ 测试数据库配置...")
    
    try:
        from almond_parser.config import settings
        
        # 测试数据库URL生成
        db_url = settings.DATABASE_URL
        print(f"  ✅ 数据库URL: {db_url}")
        
        # 检查URL格式
        if "mysql+aiomysql://" in db_url:
            print("  ✅ 异步MySQL驱动配置正确")
        else:
            print("  ⚠️ 数据库驱动可能不是异步的")
        
        # 测试各个配置项
        print(f"  📍 MySQL主机: {settings.MYSQL_HOST}")
        print(f"  📍 MySQL端口: {settings.MYSQL_PORT}")
        print(f"  📍 MySQL数据库: {settings.MYSQL_DATABASE}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库配置测试失败: {e}")
        return False


def test_alembic_env():
    """测试 Alembic 环境配置"""
    print("\n⚙️ 测试 Alembic 环境配置...")
    
    try:
        # 设置工作目录
        alembic_dir = Path(__file__).parent / "alembic"
        os.chdir(alembic_dir)
        
        # 导入 env.py
        sys.path.insert(0, str(alembic_dir))
        import env
        
        print("  ✅ env.py 导入成功")
        
        # 测试数据库URL获取
        if hasattr(env, 'get_database_url'):
            db_url = env.get_database_url()
            print(f"  ✅ 数据库URL获取成功: {db_url[:50]}...")
        else:
            print("  ❌ get_database_url 函数不存在")
        
        # 测试目标元数据
        if hasattr(env, 'target_metadata'):
            metadata = env.target_metadata
            if metadata is not None:
                print(f"  ✅ 目标元数据配置成功，包含 {len(metadata.tables)} 个表")
                
                # 列出表名
                table_names = list(metadata.tables.keys())
                print(f"  📋 数据库表: {', '.join(table_names[:5])}{'...' if len(table_names) > 5 else ''}")
            else:
                print("  ❌ 目标元数据为空")
        else:
            print("  ❌ target_metadata 不存在")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Alembic 环境配置测试失败: {e}")
        return False


def test_alembic_config_file():
    """测试 Alembic 配置文件"""
    print("\n📄 测试 Alembic 配置文件...")
    
    try:
        alembic_ini = Path(__file__).parent / "alembic.ini"
        
        if alembic_ini.exists():
            print("  ✅ alembic.ini 文件存在")
            
            # 读取配置文件
            with open(alembic_ini, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键配置
            if "file_template" in content:
                print("  ✅ 文件模板配置存在")
            
            if "script_location = alembic" in content:
                print("  ✅ 脚本位置配置正确")
            
            if "# sqlalchemy.url = driver://user:pass@localhost/dbname" in content:
                print("  ✅ 数据库URL配置已注释（将从代码获取）")
            
        else:
            print("  ❌ alembic.ini 文件不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Alembic 配置文件测试失败: {e}")
        return False


def test_alembic_manager():
    """测试 Alembic 管理器"""
    print("\n🔧 测试 Alembic 管理器...")
    
    try:
        from almond_parser.alembic_manager import cli
        print("  ✅ alembic_manager 导入成功")
        
        # 测试 click 命令
        import click
        if isinstance(cli, click.Group):
            commands = list(cli.commands.keys())
            print(f"  ✅ 可用命令: {', '.join(commands)}")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ alembic_manager 导入失败: {e}")
        print("  💡 请安装 click: pip install click")
        return False
    except Exception as e:
        print(f"  ❌ alembic_manager 测试失败: {e}")
        return False


def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    base_dir = Path(__file__).parent
    
    required_files = [
        "alembic.ini",
        "alembic/env.py",
        "alembic/script.py.mako",
        "alembic/versions/",
        "alembic_manager.py",
        "migrate.py",
        "ALEMBIC_GUIDE.md"
    ]
    
    all_exist = True
    
    for file_path in required_files:
        full_path = base_dir / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            all_exist = False
    
    return all_exist


def main():
    """主函数"""
    print("🚀 Alembic 配置测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行所有测试
    test_results.append(test_alembic_imports())
    test_results.append(test_project_imports())
    test_results.append(test_database_config())
    test_results.append(test_alembic_config_file())
    test_results.append(test_file_structure())
    test_results.append(test_alembic_manager())
    test_results.append(test_alembic_env())
    
    # 汇总结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！Alembic 配置完成。")
        print("\n✨ 下一步操作:")
        print("  1️⃣ 初始化数据库: python migrate.py init-db")
        print("  2️⃣ 查看状态: python migrate.py status")
        print("  3️⃣ 创建迁移: python migrate.py revision -m '描述'")
        print("  4️⃣ 应用迁移: python migrate.py upgrade")
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")
        failed_count = total_tests - passed_tests
        print(f"   失败的测试数量: {failed_count}")
    
    print(f"\n📚 详细使用说明请查看: {Path(__file__).parent / 'ALEMBIC_GUIDE.md'}")


if __name__ == "__main__":
    main()
