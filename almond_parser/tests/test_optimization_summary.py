#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
资源保护型节点分配优化总结测试
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_file_existence():
    """测试文件存在性"""
    print("📁 检查优化文件...")
    
    files_to_check = [
        "almond_parser/utils/node_selector.py",
        "almond_parser/utils/parse_mode_compatibility.py", 
        "almond_parser/tasks/enhanced_document_tasks.py",
        "almond_parser/tasks/retry_tasks.py",
        "almond_parser/tasks/arq_app.py",
        "almond_parser/db/models/document.py",
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            all_exist = False
    
    return all_exist


def test_syntax_check():
    """测试语法检查"""
    print("\n🔍 检查Python语法...")
    
    import subprocess
    
    files_to_check = [
        "almond_parser/utils/node_selector.py",
        "almond_parser/tasks/enhanced_document_tasks.py",
        "almond_parser/tasks/arq_app.py",
        "almond_parser/db/models/document.py",
    ]
    
    all_valid = True
    for file_path in files_to_check:
        try:
            result = subprocess.run(
                [sys.executable, "-m", "py_compile", file_path],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                print(f"  ✅ {file_path}")
            else:
                print(f"  ❌ {file_path}: {result.stderr}")
                all_valid = False
        except Exception as e:
            print(f"  ❌ {file_path}: {e}")
            all_valid = False
    
    return all_valid


def test_key_functions():
    """测试关键函数存在性"""
    print("\n🔧 检查关键函数...")
    
    try:
        # 检查节点选择器的新方法
        with open("almond_parser/utils/node_selector.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        key_methods = [
            "_find_pipeline_optimized_node",
            "_find_vlm_optimized_node", 
            "_find_pure_pipeline_node",
            "_find_sglang_node"
        ]
        
        for method in key_methods:
            if method in content:
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        # 检查增强任务的新方法
        with open("almond_parser/tasks/enhanced_document_tasks.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        key_functions = [
            "enhanced_process_document",
            "enhanced_process_document_result",
            "_check_result_quality",
            "_handle_poor_quality_result"
        ]
        
        for func in key_functions:
            if func in content:
                print(f"  ✅ {func}")
            else:
                print(f"  ❌ {func}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False


def test_database_fields():
    """测试数据库字段"""
    print("\n🗄️ 检查数据库字段...")
    
    try:
        with open("almond_parser/db/models/document.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        required_fields = [
            "original_parse_mode",
            "current_parse_mode", 
            "has_fallback",
            "retry_reason",
            "is_system_retry",
            "next_retry_at"
        ]
        
        for field in required_fields:
            if field in content and not content.count(f"# {field}") > content.count(field):
                print(f"  ✅ {field}")
            else:
                print(f"  ❌ {field} (可能被注释)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False


def test_arq_configuration():
    """测试ARQ配置"""
    print("\n⚙️ 检查ARQ配置...")
    
    try:
        with open("almond_parser/tasks/arq_app.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        required_imports = [
            "enhanced_document_tasks",
            "retry_tasks"
        ]
        
        for import_name in required_imports:
            if import_name in content:
                print(f"  ✅ 导入 {import_name}")
            else:
                print(f"  ❌ 缺少导入 {import_name}")
        
        required_functions = [
            "enhanced_process_document",
            "process_retry_documents"
        ]
        
        for func_name in required_functions:
            if func_name in content:
                print(f"  ✅ 任务函数 {func_name}")
            else:
                print(f"  ❌ 缺少任务函数 {func_name}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False


def show_optimization_summary():
    """显示优化总结"""
    print("\n" + "=" * 60)
    print("🎯 资源保护型节点分配优化总结")
    print("=" * 60)
    
    print("\n📋 已实施的优化:")
    print("  1️⃣  节点选择器优化 (node_selector.py)")
    print("     • 实现资源保护策略")
    print("     • pipe请求优先使用CPU节点")
    print("     • vlm请求直接使用GPU节点")
    
    print("\n  2️⃣  增强文档处理任务 (enhanced_document_tasks.py)")
    print("     • 支持解析结果质量检查")
    print("     • 自动降级重试机制")
    print("     • 详细的日志记录")
    
    print("\n  3️⃣  重试任务优化 (retry_tasks.py)")
    print("     • 定时轮询重试文档")
    print("     • 智能节点可用性检查")
    print("     • 过期记录清理")
    
    print("\n  4️⃣  数据库模型扩展 (document.py)")
    print("     • 添加降级重试字段")
    print("     • 支持重试状态跟踪")
    print("     • 增强错误处理")
    
    print("\n  5️⃣  ARQ任务配置 (arq_app.py)")
    print("     • 注册增强任务函数")
    print("     • 配置定时重试任务")
    print("     • 优化任务调度")
    
    print("\n🎯 核心优化策略:")
    print("  🛡️  资源保护: 优先使用专用节点，保护GPU资源")
    print("  🔄 智能降级: sglang失败时自动切换到pipeline模式")
    print("  ⏰ 自动重试: 轮询机制处理节点不可用情况")
    print("  📊 质量检查: 验证解析结果，确保输出质量")
    print("  📝 详细日志: 完整的操作追踪和调试信息")


def main():
    """主函数"""
    print("🚀 资源保护型节点分配优化验证")
    print("=" * 60)
    
    test_results = []
    
    # 执行检查
    test_results.append(test_file_existence())
    test_results.append(test_syntax_check())
    test_results.append(test_key_functions())
    test_results.append(test_database_fields())
    test_results.append(test_arq_configuration())
    
    # 汇总结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"\n📊 验证结果: {passed_tests}/{total_tests} 项检查通过")
    
    if passed_tests == total_tests:
        print("🎉 所有检查通过！优化实施成功。")
    else:
        print("⚠️  部分检查失败，请检查相关文件。")
    
    # 显示优化总结
    show_optimization_summary()
    
    print("\n✅ 优化完成！现在系统支持:")
    print("   • 智能的资源保护型节点分配")
    print("   • 自动的降级重试机制")
    print("   • 完善的错误处理和日志记录")


if __name__ == "__main__":
    main()
