#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试循环导入问题是否已解决
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_import_arq_app():
    """测试导入 arq_app 模块"""
    print("🧪 测试导入 arq_app...")
    
    try:
        from almond_parser.tasks import arq_app
        print("  ✅ arq_app 导入成功")
        
        # 测试 arq_manager 是否可用
        if hasattr(arq_app, 'arq_manager'):
            print("  ✅ arq_manager 对象存在")
        else:
            print("  ❌ arq_manager 对象不存在")
            
        return True
        
    except ImportError as e:
        print(f"  ❌ arq_app 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ arq_app 导入异常: {e}")
        return False


def test_import_enhanced_tasks():
    """测试导入增强任务模块"""
    print("\n🚀 测试导入 enhanced_document_tasks...")
    
    try:
        from almond_parser.tasks import enhanced_document_tasks
        print("  ✅ enhanced_document_tasks 导入成功")
        
        # 测试关键函数是否存在
        if hasattr(enhanced_document_tasks, 'enhanced_process_document'):
            print("  ✅ enhanced_process_document 函数存在")
        else:
            print("  ❌ enhanced_process_document 函数不存在")
            
        return True
        
    except ImportError as e:
        print(f"  ❌ enhanced_document_tasks 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ enhanced_document_tasks 导入异常: {e}")
        return False


def test_import_retry_tasks():
    """测试导入重试任务模块"""
    print("\n🔄 测试导入 retry_tasks...")
    
    try:
        from almond_parser.tasks import retry_tasks
        print("  ✅ retry_tasks 导入成功")
        
        # 测试关键函数是否存在
        if hasattr(retry_tasks, 'process_retry_documents'):
            print("  ✅ process_retry_documents 函数存在")
        else:
            print("  ❌ process_retry_documents 函数不存在")
            
        return True
        
    except ImportError as e:
        print(f"  ❌ retry_tasks 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ retry_tasks 导入异常: {e}")
        return False


def test_cross_import():
    """测试交叉导入"""
    print("\n🔗 测试交叉导入...")
    
    try:
        # 先导入 arq_app
        from almond_parser.tasks.arq_app import arq_manager
        print("  ✅ 第一步：arq_manager 导入成功")
        
        # 再导入 enhanced_document_tasks
        from almond_parser.tasks.enhanced_document_tasks import enhanced_process_document
        print("  ✅ 第二步：enhanced_process_document 导入成功")
        
        # 再导入 retry_tasks
        from almond_parser.tasks.retry_tasks import process_retry_documents
        print("  ✅ 第三步：process_retry_documents 导入成功")
        
        print("  ✅ 交叉导入测试通过")
        return True
        
    except ImportError as e:
        print(f"  ❌ 交叉导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 交叉导入异常: {e}")
        return False


def test_worker_settings():
    """测试 WorkerSettings 配置"""
    print("\n⚙️ 测试 WorkerSettings 配置...")
    
    try:
        from almond_parser.tasks.arq_app import WorkerSettings
        print("  ✅ WorkerSettings 导入成功")
        
        # 检查函数列表
        if hasattr(WorkerSettings, 'functions'):
            function_names = [func.__name__ for func in WorkerSettings.functions]
            print(f"  ✅ 配置了 {len(WorkerSettings.functions)} 个任务函数")
            
            # 检查关键函数
            expected_functions = [
                'enhanced_process_document',
                'process_retry_documents'
            ]
            
            for func_name in expected_functions:
                if func_name in function_names:
                    print(f"    ✅ {func_name}")
                else:
                    print(f"    ❌ {func_name} 未配置")
        else:
            print("  ❌ WorkerSettings.functions 不存在")
            
        # 检查定时任务
        if hasattr(WorkerSettings, 'cron_jobs'):
            print(f"  ✅ 配置了 {len(WorkerSettings.cron_jobs)} 个定时任务")
        else:
            print("  ❌ WorkerSettings.cron_jobs 不存在")
            
        return True
        
    except ImportError as e:
        print(f"  ❌ WorkerSettings 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ WorkerSettings 导入异常: {e}")
        return False


def test_delayed_import_pattern():
    """测试延迟导入模式"""
    print("\n⏰ 测试延迟导入模式...")
    
    try:
        # 模拟延迟导入
        def simulate_delayed_import():
            from almond_parser.tasks.arq_app import arq_manager
            return arq_manager
        
        manager = simulate_delayed_import()
        print("  ✅ 延迟导入模式工作正常")
        
        # 测试多次延迟导入
        for i in range(3):
            manager = simulate_delayed_import()
            print(f"    ✅ 第 {i+1} 次延迟导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 延迟导入模式失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 循环导入问题修复验证")
    print("=" * 50)
    
    test_results = []
    
    # 执行所有测试
    test_results.append(test_import_arq_app())
    test_results.append(test_import_enhanced_tasks())
    test_results.append(test_import_retry_tasks())
    test_results.append(test_cross_import())
    test_results.append(test_worker_settings())
    test_results.append(test_delayed_import_pattern())
    
    # 汇总结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！循环导入问题已解决。")
        print("\n✨ 修复方案:")
        print("  🔧 使用延迟导入避免循环依赖")
        print("  📦 在函数内部导入 arq_manager")
        print("  🚀 保持模块结构清晰")
        print("  ⚡ 不影响运行时性能")
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        failed_count = total_tests - passed_tests
        print(f"   失败的测试数量: {failed_count}")
    
    print("\n✅ 循环导入问题修复完成！")


if __name__ == "__main__":
    main()
