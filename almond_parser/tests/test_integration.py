#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
集成测试脚本 - 测试完整的解析流程
"""
import asyncio
import aiohttp
import json
from pathlib import Path
from loguru import logger

from almond_parser.config import settings


async def test_upload_and_parse():
    """测试文件上传和解析流程"""
    
    # 测试配置
    base_url = f"http://{settings.HOST}:{settings.PORT}"
    api_key = "your-test-api-key"  # 需要替换为实际的 API Key
    
    headers = {
        "Authorization": f"Bearer {api_key}"
    }
    
    try:
        # 1. 创建测试文件
        test_file_content = b"This is a test document for parsing."
        test_file_name = "test_document.txt"
        
        # 2. 上传文件
        logger.info("开始上传测试文件...")
        
        upload_url = f"{base_url}/api/v1/manage/upload"
        
        # 准备文件数据
        files = {
            'files': (test_file_name, test_file_content, 'text/plain')
        }
        data = {
            'priority': 5,
            'parse_config': json.dumps({"mode": "auto", "output_format": "markdown"})
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                upload_url,
                headers=headers,
                data=data,
                data={'files': files}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"文件上传成功: {result}")
                    
                    batch_id = result.get("batch_id")
                    if not batch_id:
                        logger.error("未获取到批次ID")
                        return
                    
                    # 3. 查询批次状态
                    await test_batch_status(session, base_url, headers, batch_id)
                    
                else:
                    error_text = await response.text()
                    logger.error(f"文件上传失败: HTTP {response.status}, {error_text}")
                    
    except Exception as e:
        logger.error(f"测试失败: {e}")


async def test_batch_status(session, base_url, headers, batch_id):
    """测试批次状态查询"""
    try:
        logger.info(f"查询批次状态: {batch_id}")
        
        status_url = f"{base_url}/api/v1/manage/batch/{batch_id}"
        
        async with session.get(status_url, headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                logger.info(f"批次状态: {result}")
                
                # 如果有文档，测试文档详情查询
                documents = result.get("documents", [])
                if documents:
                    document_id = documents[0]["document_id"]
                    await test_document_details(session, base_url, headers, document_id)
                    
            else:
                error_text = await response.text()
                logger.error(f"查询批次状态失败: HTTP {response.status}, {error_text}")
                
    except Exception as e:
        logger.error(f"查询批次状态异常: {e}")


async def test_document_details(session, base_url, headers, document_id):
    """测试文档详情查询"""
    try:
        logger.info(f"查询文档详情: {document_id}")
        
        doc_url = f"{base_url}/api/v1/manage/documents/{document_id}"
        
        async with session.get(doc_url, headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                logger.info(f"文档详情: {result}")
                
                # 测试日志查询
                await test_document_logs(session, base_url, headers, document_id)
                
            else:
                error_text = await response.text()
                logger.error(f"查询文档详情失败: HTTP {response.status}, {error_text}")
                
    except Exception as e:
        logger.error(f"查询文档详情异常: {e}")


async def test_document_logs(session, base_url, headers, document_id):
    """测试文档日志查询"""
    try:
        logger.info(f"查询文档日志: {document_id}")
        
        logs_url = f"{base_url}/api/v1/manage/documents/{document_id}/logs"
        
        async with session.get(logs_url, headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                logger.info(f"文档日志: {result}")
                
            else:
                error_text = await response.text()
                logger.error(f"查询文档日志失败: HTTP {response.status}, {error_text}")
                
    except Exception as e:
        logger.error(f"查询文档日志异常: {e}")


async def test_node_management():
    """测试节点管理功能"""
    
    base_url = f"http://{settings.HOST}:{settings.PORT}"
    api_key = "your-test-api-key"  # 需要替换为实际的 API Key
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            # 1. 查询节点列表
            logger.info("查询节点列表...")
            
            nodes_url = f"{base_url}/api/v1/mineru-nodes/"
            
            async with session.get(nodes_url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"节点列表: {result}")
                    
                    # 2. 查询节点统计
                    await test_node_stats(session, base_url, headers)
                    
                else:
                    error_text = await response.text()
                    logger.error(f"查询节点列表失败: HTTP {response.status}, {error_text}")
                    
    except Exception as e:
        logger.error(f"测试节点管理失败: {e}")


async def test_node_stats(session, base_url, headers):
    """测试节点统计"""
    try:
        logger.info("查询节点统计...")
        
        stats_url = f"{base_url}/api/v1/mineru-nodes/stats"
        
        async with session.get(stats_url, headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                logger.info(f"节点统计: {result}")
                
            else:
                error_text = await response.text()
                logger.error(f"查询节点统计失败: HTTP {response.status}, {error_text}")
                
    except Exception as e:
        logger.error(f"查询节点统计异常: {e}")


async def main():
    """主测试函数"""
    logger.info("🧪 开始集成测试...")
    
    # 测试文件上传和解析流程
    await test_upload_and_parse()
    
    # 等待一段时间
    await asyncio.sleep(2)
    
    # 测试节点管理功能
    await test_node_management()
    
    logger.info("✅ 集成测试完成")


if __name__ == "__main__":
    asyncio.run(main())
