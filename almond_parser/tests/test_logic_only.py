#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试资源保护型节点分配逻辑（不依赖数据库）
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_mode_classification():
    """测试模式分类逻辑"""
    print("🧪 测试模式分类逻辑...")
    
    # 模拟我们的分类逻辑
    def classify_request_mode(request_mode):
        """分类请求模式"""
        original_mode = request_mode.lower().strip() if request_mode else "auto"
        
        if original_mode in ["pipeline", "pipe"]:
            return "pipeline_request"
        else:
            return "vlm_request"
    
    # 测试用例
    test_cases = [
        ("pipeline", "pipeline_request"),
        ("pipe", "pipeline_request"),
        ("Pipeline", "pipeline_request"),
        ("PIPE", "pipeline_request"),
        ("sglang", "vlm_request"),
        ("vlm", "vlm_request"),
        ("vlm-sglang-client", "vlm_request"),
        ("auto", "vlm_request"),
        ("", "vlm_request"),
        (None, "vlm_request"),
    ]
    
    print("\n📋 测试结果:")
    all_passed = True
    
    for request_mode, expected in test_cases:
        result = classify_request_mode(request_mode)
        status = "✅" if result == expected else "❌"
        print(f"  {status} 请求模式: '{request_mode}' -> {result} (期望: {expected})")
        
        if result != expected:
            all_passed = False
    
    print(f"\n🎯 分类逻辑测试: {'全部通过' if all_passed else '存在失败'}")
    return all_passed


def test_resource_protection_strategy():
    """测试资源保护策略逻辑"""
    print("\n🛡️ 测试资源保护策略...")
    
    # 模拟节点数据
    mock_nodes = {
        "pipeline_nodes": [
            {"name": "cpu-node-1", "mode": "pipeline", "type": "document", "tasks": 1, "max": 3},
            {"name": "cpu-node-2", "mode": "pipeline", "type": "universal", "tasks": 0, "max": 2},
        ],
        "sglang_nodes": [
            {"name": "gpu-node-1", "mode": "sglang", "type": "document", "tasks": 2, "max": 5},
            {"name": "gpu-node-2", "mode": "vlm", "type": "universal", "tasks": 1, "max": 4},
        ]
    }
    
    def find_best_node_for_pipeline(service_type):
        """为pipeline请求查找最佳节点"""
        # 第一优先级：查找纯pipeline节点
        available_pipeline = [n for n in mock_nodes["pipeline_nodes"] 
                            if n["tasks"] < n["max"] and 
                            (n["type"] == service_type or n["type"] == "universal")]
        
        if available_pipeline:
            # 选择任务最少的
            best = min(available_pipeline, key=lambda x: x["tasks"])
            return best, "pipeline", "使用纯pipeline节点"
        
        # 第二优先级：降级到sglang节点
        available_sglang = [n for n in mock_nodes["sglang_nodes"] 
                          if n["tasks"] < n["max"] and 
                          (n["type"] == service_type or n["type"] == "universal")]
        
        if available_sglang:
            best = min(available_sglang, key=lambda x: x["tasks"])
            return best, "pipeline", "降级使用sglang节点"
        
        return None, None, "无可用节点"
    
    def find_best_node_for_vlm(service_type):
        """为VLM请求查找最佳节点"""
        # 直接查找sglang节点
        available_sglang = [n for n in mock_nodes["sglang_nodes"] 
                          if n["tasks"] < n["max"] and 
                          (n["type"] == service_type or n["type"] == "universal")]
        
        if available_sglang:
            best = min(available_sglang, key=lambda x: x["tasks"])
            return best, "sglang", "使用sglang节点"
        
        return None, None, "无可用节点"
    
    # 测试场景
    test_scenarios = [
        ("document", "pipeline", find_best_node_for_pipeline),
        ("document", "sglang", find_best_node_for_vlm),
        ("universal", "pipeline", find_best_node_for_pipeline),
        ("universal", "vlm", find_best_node_for_vlm),
    ]
    
    print("\n📋 策略测试结果:")
    
    for service_type, request_mode, finder_func in test_scenarios:
        node, exec_mode, reason = finder_func(service_type)
        
        print(f"\n  🎯 场景: {service_type} 服务 + {request_mode} 请求")
        if node:
            print(f"     ✅ 选中节点: {node['name']}")
            print(f"        节点模式: {node['mode']}")
            print(f"        执行模式: {exec_mode}")
            print(f"        当前负载: {node['tasks']}/{node['max']}")
            print(f"        选择原因: {reason}")
            
            # 验证资源保护策略
            if request_mode == "pipeline":
                if node['mode'] == 'pipeline':
                    print(f"        🎯 资源保护成功: 优先使用了CPU节点")
                else:
                    print(f"        ⚠️  合理降级: 使用了GPU节点（无可用CPU节点）")
            else:
                if node['mode'] in ['sglang', 'vlm']:
                    print(f"        🎯 VLM分配成功: 使用了GPU节点")
        else:
            print(f"     ❌ {reason}")
    
    return True


def test_compatibility_matrix():
    """测试兼容性矩阵逻辑"""
    print("\n🔗 测试兼容性矩阵...")
    
    # 模拟兼容性矩阵
    COMPATIBILITY_MATRIX = {
        "sglang": ["sglang", "vlm", "vlm-sglang-client", "pipeline", "auto"],
        "vlm": ["sglang", "vlm", "vlm-sglang-client", "pipeline", "auto"],
        "pipeline": ["pipeline", "auto"],
        "auto": ["sglang", "vlm", "vlm-sglang-client", "pipeline", "auto"],
    }
    
    def can_handle_request(node_mode, request_mode):
        """检查节点是否可以处理请求"""
        compatible_modes = COMPATIBILITY_MATRIX.get(node_mode, [])
        return request_mode in compatible_modes
    
    # 测试用例
    test_cases = [
        ("sglang", "pipeline", True, "sglang节点应该能处理pipeline请求"),
        ("sglang", "sglang", True, "sglang节点应该能处理sglang请求"),
        ("pipeline", "sglang", False, "pipeline节点不能处理sglang请求"),
        ("pipeline", "pipeline", True, "pipeline节点应该能处理pipeline请求"),
        ("vlm", "pipeline", True, "vlm节点应该能处理pipeline请求"),
    ]
    
    print("\n📋 兼容性测试结果:")
    all_passed = True
    
    for node_mode, request_mode, expected, description in test_cases:
        result = can_handle_request(node_mode, request_mode)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {description}")
        print(f"      节点模式: {node_mode}, 请求模式: {request_mode}, 结果: {result}")
        
        if result != expected:
            all_passed = False
    
    print(f"\n🎯 兼容性测试: {'全部通过' if all_passed else '存在失败'}")
    return all_passed


def main():
    """主函数"""
    print("🚀 开始测试资源保护型节点分配逻辑...")
    
    results = []
    results.append(test_mode_classification())
    results.append(test_resource_protection_strategy())
    results.append(test_compatibility_matrix())
    
    print(f"\n✅ 测试完成: {sum(results)}/{len(results)} 项测试通过")
    
    if all(results):
        print("🎉 所有测试通过！资源保护策略逻辑正确。")
    else:
        print("⚠️  部分测试失败，需要检查逻辑。")


if __name__ == "__main__":
    main()
