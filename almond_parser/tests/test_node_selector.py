#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试节点选择器功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from almond_parser.db.database import get_db
from almond_parser.utils.node_selector import NodeSelector, select_document_node, select_knowledge_base_node


async def test_node_selector():
    """测试节点选择器"""
    print("🧪 开始测试节点选择器...")
    
    try:
        async for db in get_db():
            selector = NodeSelector(db)
            
            # 测试获取服务类型统计
            print("\n📊 获取服务类型统计:")
            stats = await selector.get_service_type_stats()
            for service_type, data in stats.items():
                print(f"  {service_type}: 总计 {data['total']}, 在线 {data['online']}, 离线 {data['offline']}")
            
            # 测试选择文档解析节点
            print("\n📄 选择文档解析节点:")
            doc_node = await select_document_node(db)
            if doc_node:
                print(f"  选中节点: {doc_node.name} (类型: {doc_node.service_type.value})")
            else:
                print("  没有可用的文档解析节点")
            
            # 测试选择知识库解析节点
            print("\n📚 选择知识库解析节点:")
            kb_node = await select_knowledge_base_node(db)
            if kb_node:
                print(f"  选中节点: {kb_node.name} (类型: {kb_node.service_type.value})")
            else:
                print("  没有可用的知识库解析节点")
            
            # 测试获取各类型节点列表
            print("\n📋 获取文档解析节点列表:")
            doc_nodes = await selector.get_nodes_by_service_type("document")
            for node in doc_nodes:
                print(f"  - {node.name} (类型: {node.service_type.value}, 任务: {node.current_tasks}/{node.max_concurrent_tasks})")
            
            print("\n📋 获取知识库解析节点列表:")
            kb_nodes = await selector.get_nodes_by_service_type("knowledge_base")
            for node in kb_nodes:
                print(f"  - {node.name} (类型: {node.service_type.value}, 任务: {node.current_tasks}/{node.max_concurrent_tasks})")
            
            break
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    await test_node_selector()
    print("\n✅ 测试完成")


if __name__ == "__main__":
    asyncio.run(main())
