#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
为 api_keys 表添加 updated_at 字段的迁移脚本
"""

import pymysql
import os
from pathlib import Path

def migrate():
    """执行迁移"""
    try:
        # 从环境变量获取数据库配置
        db_config = {
            'host': os.getenv('MYSQL_HOST', 'localhost'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER', 'root'),
            'password': os.getenv('MYSQL_PASSWORD', '123456'),
            'database': os.getenv('MYSQL_DATABASE', 'almond_parser'),
            'charset': 'utf8mb4'
        }

        print(f"连接数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")

        # 连接数据库
        connection = pymysql.connect(**db_config)

        try:
            with connection.cursor() as cursor:
                # 检查 updated_at 字段是否已存在
                check_column_sql = """
                SELECT COUNT(*) as count
                FROM information_schema.columns
                WHERE table_name = 'api_keys'
                AND column_name = 'updated_at'
                AND table_schema = %s
                """

                cursor.execute(check_column_sql, (db_config['database'],))
                result = cursor.fetchone()

                if result and result[0] > 0:
                    print("✅ updated_at 字段已存在，跳过迁移")
                    return

                # 添加 updated_at 字段
                add_column_sql = """
                ALTER TABLE api_keys
                ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
                """

                cursor.execute(add_column_sql)
                print("✅ 成功添加 updated_at 字段到 api_keys 表")

                # 为现有记录设置 updated_at 值
                update_existing_sql = """
                UPDATE api_keys
                SET updated_at = created_at
                WHERE updated_at IS NULL
                """

                cursor.execute(update_existing_sql)
                print("✅ 成功为现有记录设置 updated_at 值")

                # 提交事务
                connection.commit()
                print("✅ 迁移完成")

        finally:
            connection.close()

    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        raise


if __name__ == "__main__":
    migrate()
