# -*- encoding: utf-8 -*-
"""
数据库迁移脚本 - 添加重试和降级相关字段
"""
import asyncio
from datetime import datetime
from loguru import logger
from sqlalchemy import text
from almond_parser.db.database import get_async_session


async def migrate_add_retry_fields():
    """添加重试和降级相关字段"""
    logger.info("开始数据库迁移：添加重试和降级字段...")
    
    async with get_async_session() as db:
        try:
            # 检查字段是否已存在
            check_columns_sql = """
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'documents'
            AND COLUMN_NAME IN (
                'original_parse_mode', 'current_parse_mode', 'has_fallback',
                'retry_reason', 'is_system_retry', 'next_retry_at'
            )
            """
            
            result = await db.execute(text(check_columns_sql))
            existing_columns = [row[0] for row in result.fetchall()]
            
            # 需要添加的字段
            fields_to_add = [
                ("original_parse_mode", "VARCHAR(50) NULL COMMENT '原始解析模式'"),
                ("current_parse_mode", "VARCHAR(50) NULL COMMENT '当前解析模式'"),
                ("has_fallback", "BOOLEAN DEFAULT FALSE COMMENT '是否已降级'"),
                ("retry_reason", "VARCHAR(100) NULL COMMENT '重试原因'"),
                ("is_system_retry", "BOOLEAN DEFAULT FALSE COMMENT '是否为系统重试'"),
                ("next_retry_at", "DATETIME NULL COMMENT '下次重试时间'")
            ]
            
            # 添加缺失的字段
            for field_name, field_definition in fields_to_add:
                if field_name not in existing_columns:
                    alter_sql = f"ALTER TABLE documents ADD COLUMN {field_name} {field_definition}"
                    await db.execute(text(alter_sql))
                    logger.info(f"✅ 添加字段: {field_name}")
                else:
                    logger.info(f"⏭️  字段已存在: {field_name}")
            
            # 检查并添加新的枚举值到 status 字段
            await _update_status_enum(db)
            
            await db.commit()
            logger.info("✅ 数据库迁移完成")
            
        except Exception as e:
            await db.rollback()
            logger.error(f"❌ 数据库迁移失败: {e}")
            raise


async def _update_status_enum(db):
    """更新状态枚举值"""
    try:
        # 检查当前枚举值
        check_enum_sql = """
        SELECT COLUMN_TYPE 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'documents' 
        AND COLUMN_NAME = 'status'
        """
        
        result = await db.execute(text(check_enum_sql))
        current_enum = result.fetchone()
        
        if current_enum:
            enum_values = current_enum[0]
            logger.info(f"当前状态枚举: {enum_values}")
            
            # 检查是否需要添加新的枚举值
            new_values = ['RETRY_PENDING', 'FALLBACK_RETRY']
            needs_update = any(value not in enum_values for value in new_values)
            
            if needs_update:
                # 修改枚举类型
                alter_enum_sql = """
                ALTER TABLE documents 
                MODIFY COLUMN status ENUM(
                    'UPLOADING', 'UPLOADED', 'PARSING', 'COMPLETED', 
                    'FAILED', 'RETRY_PENDING', 'FALLBACK_RETRY'
                ) DEFAULT 'UPLOADED'
                """
                
                await db.execute(text(alter_enum_sql))
                logger.info("✅ 更新状态枚举值")
            else:
                logger.info("⏭️  状态枚举值已是最新")
                
    except Exception as e:
        logger.error(f"更新状态枚举失败: {e}")
        raise


async def rollback_retry_fields():
    """回滚迁移 - 删除添加的字段"""
    logger.info("开始回滚数据库迁移：删除重试和降级字段...")
    
    async with get_async_session() as db:
        try:
            # 要删除的字段
            fields_to_remove = [
                "original_parse_mode",
                "current_parse_mode", 
                "has_fallback",
                "retry_reason",
                "is_system_retry",
                "next_retry_at"
            ]
            
            for field_name in fields_to_remove:
                try:
                    alter_sql = f"ALTER TABLE documents DROP COLUMN {field_name}"
                    await db.execute(text(alter_sql))
                    logger.info(f"✅ 删除字段: {field_name}")
                except Exception as e:
                    logger.warning(f"删除字段失败 {field_name}: {e}")
            
            # 恢复原始枚举值
            restore_enum_sql = """
            ALTER TABLE documents 
            MODIFY COLUMN status ENUM(
                'UPLOADING', 'UPLOADED', 'PARSING', 'COMPLETED', 'FAILED'
            ) DEFAULT 'UPLOADED'
            """
            
            await db.execute(text(restore_enum_sql))
            logger.info("✅ 恢复状态枚举值")
            
            await db.commit()
            logger.info("✅ 数据库回滚完成")
            
        except Exception as e:
            await db.rollback()
            logger.error(f"❌ 数据库回滚失败: {e}")
            raise


async def check_migration_status():
    """检查迁移状态"""
    logger.info("检查数据库迁移状态...")
    
    async with get_async_session() as db:
        try:
            # 检查字段是否存在
            check_sql = """
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'documents'
            AND COLUMN_NAME IN (
                'original_parse_mode', 'current_parse_mode', 'has_fallback',
                'retry_reason', 'is_system_retry', 'next_retry_at'
            )
            ORDER BY COLUMN_NAME
            """
            
            result = await db.execute(text(check_sql))
            columns = result.fetchall()
            
            if columns:
                logger.info("📋 已添加的重试字段:")
                for col in columns:
                    logger.info(f"  - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'}) {col[4] or ''}")
            else:
                logger.info("❌ 未找到重试相关字段")
            
            # 检查状态枚举
            enum_sql = """
            SELECT COLUMN_TYPE 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'documents' 
            AND COLUMN_NAME = 'status'
            """
            
            result = await db.execute(text(enum_sql))
            enum_info = result.fetchone()
            
            if enum_info:
                logger.info(f"📋 当前状态枚举: {enum_info[0]}")
            
        except Exception as e:
            logger.error(f"检查迁移状态失败: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        action = sys.argv[1]
        if action == "migrate":
            asyncio.run(migrate_add_retry_fields())
        elif action == "rollback":
            asyncio.run(rollback_retry_fields())
        elif action == "check":
            asyncio.run(check_migration_status())
        else:
            print("用法: python add_retry_fields.py [migrate|rollback|check]")
    else:
        print("用法: python add_retry_fields.py [migrate|rollback|check]")
        print("  migrate  - 执行迁移")
        print("  rollback - 回滚迁移")
        print("  check    - 检查迁移状态")
