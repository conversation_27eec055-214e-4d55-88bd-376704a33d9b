"""add api_key_id to documents table

Revision ID: add_api_key_id_to_documents
Revises: 
Create Date: 2025-01-07 20:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_api_key_id_to_documents'
down_revision = None  # 请根据实际情况修改为上一个版本的revision
depends_on = None


def upgrade():
    """添加api_key_id字段到documents表"""
    # 添加api_key_id列
    op.add_column('documents', sa.Column('api_key_id', sa.Integer(), nullable=True, comment='上传时使用的API Key ID'))
    
    # 可选：添加外键约束（如果需要的话）
    # op.create_foreign_key('fk_documents_api_key_id', 'documents', 'api_keys', ['api_key_id'], ['id'])


def downgrade():
    """移除api_key_id字段"""
    # 移除外键约束（如果添加了的话）
    # op.drop_constraint('fk_documents_api_key_id', 'documents', type_='foreignkey')
    
    # 移除列
    op.drop_column('documents', 'api_key_id')
