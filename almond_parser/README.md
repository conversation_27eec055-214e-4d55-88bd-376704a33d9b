# 杏仁解析服务 (Almond Parser)

基于 FastAPI + ARQ 的轻量级文档解析和节点管理服务。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

创建 `.env` 文件：

```env
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=almond_parser

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 应用配置
DEBUG=true
JWT_SECRET_KEY=your-secret-key-here
```

### 3. 启动服务

#### 启动 Web 服务
```bash
python -m almond_parser.main
```

#### 启动 ARQ 工作器
```bash
python -m almond_parser.worker
```

## 🏗️ 架构特点

### 轻量级异步架构
- **FastAPI**: 高性能异步 Web 框架
- **ARQ**: 轻量级异步任务队列，完全兼容 asyncio
- **aioredis**: 异步 Redis 客户端
- **aiomysql**: 异步 MySQL 客户端

### 单例连接池管理
- MySQL 连接池自动管理，支持连接回收和健康检查
- Redis 连接池统一管理，避免连接泄漏
- 异步会话管理，自动处理事务和异常

### 结构化日志
- 基于 Loguru 的结构化日志系统
- 分级日志记录（控制台、文件、错误）
- 支持日志轮转和压缩

## 📁 项目结构

```
almond_parser/
├── config.py              # 配置管理
├── main.py                # FastAPI 应用入口
├── worker.py              # ARQ 工作器启动脚本
├── api/                   # API 路由层
├── schemas/               # Pydantic 模型
├── db/                    # 数据库层
│   ├── database.py        # MySQL 连接管理
│   ├── redis_client.py    # Redis 连接管理
│   └── models/            # 数据库模型
├── tasks/                 # ARQ 异步任务
│   ├── arq_app.py         # ARQ 配置
│   ├── document_tasks.py  # 文档处理任务
│   └── node_tasks.py      # 节点管理任务
├── services/              # 业务逻辑层
└── utils/                 # 工具层
    ├── auth.py            # 认证工具
    └── logger.py          # 日志配置
```

## 🔧 核心功能

### 文档管理
- 文档上传和解析
- 批量任务处理
- 实时进度跟踪
- 任务重试机制

### 节点管理
- MinerU 节点注册和管理
- 自动健康检查
- 负载均衡
- 节点状态监控

### 任务队列
- 基于 ARQ 的异步任务处理
- 支持任务重试和延迟执行
- 定期任务调度
- 任务结果持久化

## 🛠️ 开发说明

### 为什么选择 ARQ 而不是 Celery？

1. **异步友好**: ARQ 专为 asyncio 设计，与 FastAPI 完美集成
2. **轻量级**: 比 Celery 轻量 90%，配置简单
3. **性能优秀**: 真正的异步执行，无事件循环冲突
4. **维护简单**: 代码简洁，学习成本低

### 任务示例

```python
# 提交文档处理任务
from almond_parser.tasks.arq_app import arq_manager

job_id = await arq_manager.enqueue_task(
    "process_document",
    document_id="doc_123",
    user_id="user_456"
)

# 查询任务结果
result = await arq_manager.get_job_result(job_id)
```

## 📝 API 接口总览

### 🔐 管理后台接口
```
POST   /api/v1/manage/login                    # 管理员登录
POST   /api/v1/manage/register                 # 用户注册
GET    /api/v1/manage/me                       # 获取当前用户信息
GET    /api/v1/manage/documents                # 查询文档记录（管理员）
POST   /api/v1/manage/retry/{document_id}      # 手动重试解析任务
GET    /api/v1/manage/mineru/task-counts       # 查看节点任务计数状态
POST   /api/v1/manage/mineru/check-task-counts # 检查并修复任务计数
POST   /api/v1/manage/mineru/reset-task-counts # 重置所有节点任务计数
POST   /api/v1/manage/mineru/emergency-reset   # 异步执行紧急重置
```

### 📄 文档管理接口
```
POST   /api/v1/manage/upload                   # 批量文件上传
GET    /api/v1/manage/documents                # 获取文档列表
GET    /api/v1/manage/documents/{id}           # 获取文档详情
POST   /api/v1/manage/documents/{id}/retry     # 重试文档
DELETE /api/v1/manage/documents/{id}           # 删除文档
GET    /api/v1/manage/batch/{id}               # 获取批次状态
POST   /api/v1/manage/callback/parse-result    # 接收解析结果回调
GET    /api/v1/manage/documents/{id}/result    # 获取解析结果
POST   /api/v1/manage/documents/{id}/query-status # 手动查询状态
GET    /api/v1/manage/documents/{id}/logs      # 获取文档日志
```

### 🖥️ 节点管理接口
```
POST   /api/v1/mineru-nodes/                   # 创建节点
GET    /api/v1/mineru-nodes/                   # 获取节点列表
GET    /api/v1/mineru-nodes/stats              # 获取节点统计
GET    /api/v1/mineru-nodes/{id}               # 获取节点详情
PUT    /api/v1/mineru-nodes/{id}               # 更新节点
DELETE /api/v1/mineru-nodes/{id}               # 删除节点
POST   /api/v1/mineru-nodes/health-check       # 触发所有节点健康检查
GET    /api/v1/mineru-nodes/{id}/health        # 获取节点健康状态
POST   /api/v1/mineru-nodes/{id}/health-check  # 触发单个节点健康检查
```

### 🔑 API Key 管理接口
```
POST   /api/v1/api-keys/                       # 创建 API Key
GET    /api/v1/api-keys/                       # 获取 API Key 列表
PUT    /api/v1/api-keys/{id}                   # 更新 API Key
DELETE /api/v1/api-keys/{id}                   # 删除 API Key
```

### 🌐 WebSocket 接口
```
WS     /api/v1/manage/ws/logs/document/{id}    # 文档日志 WebSocket
WS     /api/v1/manage/ws/status                # 系统状态 WebSocket
```

## 🧪 测试

### 运行集成测试
```bash
python -m almond_parser.test_integration
```

### 运行管理后台测试
```bash
python -m almond_parser.test_manage_api
```

## ✅ 已完成功能

- [x] FastAPI 主应用
- [x] API 路由实现
- [x] WebSocket 支持
- [x] 服务层业务逻辑
- [x] 前端接口对接
- [x] 管理后台重构
- [x] 多文件上传和解析
- [x] MinerU API 交互
- [x] 回调接口和手动查询
- [x] ARQ 异步任务队列
