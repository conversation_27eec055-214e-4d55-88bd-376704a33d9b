"""初始化

Revision ID: b1025bb3ac91
Revises: 
Create Date: 2025-06-26 22:26:35.731429

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'b1025bb3ac91'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('api_keys', 'updated_at',
               existing_type=mysql.DATETIME(),
               server_default=None,
               existing_comment='更新时间',
               existing_nullable=True)
    op.add_column('documents', sa.Column('original_parse_mode', sa.String(length=50), nullable=True, comment='原始解析模式'))
    op.add_column('documents', sa.Column('current_parse_mode', sa.String(length=50), nullable=True, comment='当前解析模式'))
    op.add_column('documents', sa.Column('has_fallback', sa.Boolean(), nullable=True, comment='是否已降级'))
    op.add_column('documents', sa.Column('retry_reason', sa.String(length=100), nullable=True, comment='重试原因'))
    op.add_column('documents', sa.Column('is_system_retry', sa.Boolean(), nullable=True, comment='是否为系统重试'))
    op.add_column('documents', sa.Column('next_retry_at', sa.DateTime(), nullable=True, comment='下次重试时间'))
    op.alter_column('documents', 'task_id',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_0900_ai_ci', length=100),
               comment='解析任务ID',
               existing_comment='解析任务id',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('documents', 'task_id',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_0900_ai_ci', length=100),
               comment='解析任务id',
               existing_comment='解析任务ID',
               existing_nullable=True)
    op.drop_column('documents', 'next_retry_at')
    op.drop_column('documents', 'is_system_retry')
    op.drop_column('documents', 'retry_reason')
    op.drop_column('documents', 'has_fallback')
    op.drop_column('documents', 'current_parse_mode')
    op.drop_column('documents', 'original_parse_mode')
    op.alter_column('api_keys', 'updated_at',
               existing_type=mysql.DATETIME(),
               server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
               existing_comment='更新时间',
               existing_nullable=True)
    # ### end Alembic commands ###
