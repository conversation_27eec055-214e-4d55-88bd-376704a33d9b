"""初始化

Revision ID: e4f7e8a1f2a3
Revises: b1025bb3ac91
Create Date: 2025-06-26 22:58:10.440950

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e4f7e8a1f2a3'
down_revision: Union[str, Sequence[str], None] = 'b1025bb3ac91'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('api_keys', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment='更新时间',
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('api_keys', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=True)
    # ### end Alembic commands ###
