# -*- coding: utf-8 -*-


import asyncio
import os
import sys
from pathlib import Path

# 添加根路径到 sys.path
project_root = Path(__file__).resolve().parents[2]
sys.path.insert(0, str(project_root))
from logging.config import fileConfig


from sqlalchemy.ext.asyncio import create_async_engine
from alembic import context

from almond_parser.config import settings
from almond_parser.db.database import Base

# 导入所有模型，确保 Base.metadata 包含全部表
from almond_parser.db.models import api_key, document, mineru_node, user



# Alembic config 对象
config = context.config

# 设置日志
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# 指定元数据
target_metadata = Base.metadata


# 获取数据库连接地址（替换 async 协议）
def get_url():
    return settings.database_url.replace("mysql+aiomysql", "mysql+pymysql")


# 离线迁移
def run_migrations_offline():
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        compare_type=True,
        compare_server_default=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


# 在线迁移
async def run_migrations_online():
    connectable = create_async_engine(
        settings.database_url,
        poolclass=None,
        echo=False,
    )

    async with connectable.begin() as conn:
        def do_run_migrations(sync_conn):
            context.configure(
                connection=sync_conn,
                target_metadata=target_metadata,
                compare_type=True,
                compare_server_default=True,
            )
            with context.begin_transaction():
                context.run_migrations()

        await conn.run_sync(do_run_migrations)

    await connectable.dispose()


def do_run_migrations(sync_conn):
    context.configure(
        connection=sync_conn,
        target_metadata=target_metadata,
        compare_type=True,
        compare_server_default=True,
    )
    with context.begin_transaction():
        context.run_migrations()



if context.is_offline_mode():
    run_migrations_offline()
else:
    asyncio.run(run_migrations_online())
