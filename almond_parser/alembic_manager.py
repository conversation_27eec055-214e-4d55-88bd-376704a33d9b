#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
Alembic 数据库迁移管理工具
"""

import os
import sys
import subprocess
from pathlib import Path
import click

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_alembic_command(command: str, *args) -> int:
    """运行 alembic 命令"""
    alembic_dir = Path(__file__).parent
    cmd = ["alembic", "-c", str(alembic_dir / "alembic.ini"), command] + list(args)
    
    print(f"🔧 执行命令: {' '.join(cmd)}")
    
    # 设置工作目录为 almond_parser
    result = subprocess.run(cmd, cwd=alembic_dir)
    return result.returncode


@click.group()
def cli():
    """Alembic 数据库迁移管理工具"""
    pass


@cli.command()
@click.option('--message', '-m', required=True, help='迁移描述信息')
@click.option('--autogenerate/--no-autogenerate', default=True, help='是否自动生成迁移')
def revision(message: str, autogenerate: bool):
    """创建新的数据库迁移"""
    print(f"📝 创建新迁移: {message}")
    
    args = ['--message', message]
    if autogenerate:
        args.append('--autogenerate')
    
    return run_alembic_command('revision', *args)


@cli.command()
@click.argument('revision', default='head')
def upgrade(revision: str):
    """升级数据库到指定版本"""
    print(f"⬆️ 升级数据库到: {revision}")
    return run_alembic_command('upgrade', revision)


@cli.command()
@click.argument('revision')
def downgrade(revision: str):
    """降级数据库到指定版本"""
    print(f"⬇️ 降级数据库到: {revision}")
    return run_alembic_command('downgrade', revision)


@cli.command()
@click.option('--verbose', '-v', is_flag=True, help='显示详细信息')
def current(verbose: bool):
    """显示当前数据库版本"""
    print("📍 当前数据库版本:")
    args = []
    if verbose:
        args.append('--verbose')
    return run_alembic_command('current', *args)


@cli.command()
@click.option('--verbose', '-v', is_flag=True, help='显示详细信息')
def history(verbose: bool):
    """显示迁移历史"""
    print("📚 迁移历史:")
    args = []
    if verbose:
        args.append('--verbose')
    return run_alembic_command('history', *args)


@cli.command()
@click.argument('revision', default='head')
def show(revision: str):
    """显示指定迁移的详细信息"""
    print(f"🔍 显示迁移详情: {revision}")
    return run_alembic_command('show', revision)


@cli.command()
def check():
    """检查数据库是否与模型同步"""
    print("🔍 检查数据库同步状态...")
    return run_alembic_command('check')


@cli.command()
def init_db():
    """初始化数据库（创建初始迁移并升级）"""
    print("🚀 初始化数据库...")
    
    # 检查是否已有迁移文件
    versions_dir = Path(__file__).parent / "alembic" / "versions"
    if list(versions_dir.glob("*.py")):
        print("⚠️ 已存在迁移文件，请使用 upgrade 命令")
        return 1
    
    # 创建初始迁移
    print("📝 创建初始迁移...")
    result = run_alembic_command('revision', '--autogenerate', '--message', 'Initial migration')
    if result != 0:
        print("❌ 创建初始迁移失败")
        return result
    
    # 升级到最新版本
    print("⬆️ 升级到最新版本...")
    result = run_alembic_command('upgrade', 'head')
    if result != 0:
        print("❌ 数据库升级失败")
        return result
    
    print("✅ 数据库初始化完成")
    return 0


@cli.command()
def reset_db():
    """重置数据库（危险操作）"""
    print("⚠️ 这将删除所有迁移历史并重新初始化数据库")
    
    if not click.confirm("确定要继续吗？"):
        print("❌ 操作已取消")
        return 0
    
    # 删除所有迁移文件
    versions_dir = Path(__file__).parent / "alembic" / "versions"
    for migration_file in versions_dir.glob("*.py"):
        migration_file.unlink()
        print(f"🗑️ 删除迁移文件: {migration_file.name}")
    
    # 重新初始化
    return init_db()


@cli.command()
def status():
    """显示数据库和迁移状态"""
    print("📊 数据库迁移状态:")
    print("=" * 50)
    
    # 显示当前版本
    print("📍 当前版本:")
    run_alembic_command('current', '--verbose')
    
    print("\n📚 最近的迁移:")
    run_alembic_command('history', '--rev-range', 'current:head')
    
    # 检查同步状态
    print("\n🔍 同步检查:")
    result = run_alembic_command('check')
    if result == 0:
        print("✅ 数据库与模型同步")
    else:
        print("⚠️ 数据库与模型不同步，建议创建新迁移")


@cli.command()
def auto_migrate():
    """自动迁移（检查变更并应用）"""
    print("🤖 自动迁移模式...")
    
    # 检查是否需要迁移
    print("🔍 检查数据库同步状态...")
    result = run_alembic_command('check')
    
    if result == 0:
        print("✅ 数据库已是最新状态")
        return 0
    
    # 创建新迁移
    print("📝 检测到模型变更，创建新迁移...")
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    message = f"Auto migration {timestamp}"
    
    result = run_alembic_command('revision', '--autogenerate', '--message', message)
    if result != 0:
        print("❌ 创建迁移失败")
        return result
    
    # 应用迁移
    print("⬆️ 应用新迁移...")
    result = run_alembic_command('upgrade', 'head')
    if result != 0:
        print("❌ 应用迁移失败")
        return result
    
    print("✅ 自动迁移完成")
    return 0


if __name__ == '__main__':
    cli()
