# -*- encoding: utf-8 -*-
"""
杏仁解析服务配置管理
"""
import os
from typing import Optional
from urllib.parse import quote_plus

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""

    # 应用基础配置
    APP_NAME: str = Field(default="杏仁解析服务", description="应用名称")
    APP_VERSION: str = Field(default="1.0.0", description="应用版本")
    DEBUG: bool = Field(default=False, description="调试模式")
    HOST: str = Field(default="0.0.0.0", description="服务主机")
    PORT: int = Field(default=8010, description="服务端口")
    HOST_URL: str = Field(default="http://*************:8010")

    # 数据库配置
    MYSQL_HOST: str = Field(default="localhost", description="MySQL主机")
    MYSQL_PORT: int = Field(default=3306, description="MySQL端口")
    MYSQL_USER: str = Field(default="root", description="MySQL用户名")
    MYSQL_PASSWORD: str = Field(default="123456", description="MySQL密码")
    MYSQL_DATABASE: str = Field(default="almond_parser", description="MySQL数据库名")
    MYSQL_CHARSET: str = Field(default="utf8mb4", description="MySQL字符集")

    # 数据库连接池配置
    DB_POOL_SIZE: int = Field(default=20, description="数据库连接池大小")
    DB_MAX_OVERFLOW: int = Field(default=30, description="数据库连接池最大溢出")
    DB_POOL_TIMEOUT: int = Field(default=10, description="数据库连接池超时时间")
    DB_POOL_RECYCLE: int = Field(default=1800, description="数据库连接回收时间(30分钟)")

    # Redis配置
    REDIS_HOST: str = Field(default="127.0.0.1", description="Redis主机")
    REDIS_PORT: int = Field(default=6379, description="Redis端口")
    REDIS_PASSWORD: Optional[str] = Field(default=None, description="Redis密码")
    REDIS_DB: int = Field(default=0, description="Redis数据库")
    REDIS_POOL_SIZE: int = Field(default=10, description="Redis连接池大小")

    # ARQ 任务队列配置
    ARQ_REDIS_DB: int = Field(default=1, description="ARQ Redis 数据库")
    ARQ_MAX_JOBS: int = Field(default=10, description="ARQ 最大并发任务数")
    ARQ_JOB_TIMEOUT: int = Field(default=300, description="ARQ 任务超时时间(秒)")
    ARQ_KEEP_RESULT: int = Field(default=3600, description="ARQ 结果保留时间(秒)")
    ARQ_MAX_TRIES: int = Field(default=3, description="ARQ 最大重试次数")
    ARQ_RETRY_DELAY: int = Field(default=60, description="ARQ 重试延迟(秒)")

    # JWT配置
    JWT_SECRET_KEY: str = Field(default="your-secret-key-here", description="JWT密钥")
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT算法")
    JWT_EXPIRE_MINUTES: int = Field(default=60 * 24 * 7, description="JWT过期时间(分钟)")

    # 管理员创建密钥
    SUPER_KEY: str = Field(default="admin123", description="创建管理员账号的超级密钥")

    # API Key配置
    API_KEY_LENGTH: int = Field(default=32, description="API Key长度")
    API_KEY_PREFIX: str = Field(default="ak_", description="API Key前缀")

    # 文件上传配置
    UPLOAD_DIR: str = Field(default="./uploads", description="上传目录")
    OUTPUT_DIR: str = Field(default="./output", description="输出目录")
    MAX_FILE_SIZE_MB: int = Field(default=300, description="最大文件大小(MB)")

    ALLOWED_EXTENSIONS: list = Field(
        default=[".pdf", ".docx", ".doc", ".pptx", ".ppt", ".jpg", ".jpeg", ".png"],
        description="允许的文件扩展名"
    )

    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_DIR: str = Field(default="./logs", description="日志目录")
    LOG_ROTATION: str = Field(default="7 day", description="日志轮转")
    LOG_RETENTION: str = Field(default="30 days", description="日志保留时间")

    # MinerU节点配置
    NODE_HEALTH_CHECK_INTERVAL: int = Field(default=60, description="节点健康检查间隔(秒)")
    NODE_MAX_CONCURRENT_TASKS: int = Field(default=3, description="节点最大并发任务数")
    NODE_REQUEST_TIMEOUT: int = Field(default=30, description="节点请求超时时间(秒)")

    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = Field(default=30, description="WebSocket心跳间隔(秒)")
    WS_MAX_CONNECTIONS: int = Field(default=100, description="WebSocket最大连接数")

    # MinerU API 交互配置
    MINERU_API_TIMEOUT: int = Field(default=30, description="MinerU API 请求超时时间(秒)")
    CALLBACK_BASE_URL: str = Field(default="http://localhost:8010", description="回调基础URL")
    PARSE_RESULT_RETENTION: int = Field(default=7 * 24 * 3600, description="解析结果保留时间(秒)")

    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=60 * 24 * 7, description="JWT访问令牌过期时间(分钟)")
    MINERU_API_BASE_URL: str = Field(default="http://localhost:8000", description="MinerU API 基础URL")

    @property
    def database_url(self) -> str:
        """获取数据库连接URL"""
        user = quote_plus(self.MYSQL_USER)
        password = quote_plus(self.MYSQL_PASSWORD)
        return (
            f"mysql+aiomysql://{user}:{password}"
            f"@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{self.MYSQL_DATABASE}"
            f"?charset={self.MYSQL_CHARSET}"
        )


    @property
    def DATABASE_URL(self) -> str:
        """获取数据库连接URL（Alembic兼容）"""
        return self.database_url

    @property
    def redis_url(self) -> str:
        """获取Redis连接URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"

    @property
    def MAX_FILE_SIZE(self) -> int:
        return self.MAX_FILE_SIZE_MB * 1024 * 1024

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 全局配置实例
settings = Settings()
