#!/usr/bin/env python3
"""
AI中心解析流 - 打包构建脚本

支持构建 whl 包并发布到 PyPI 或私有仓库
"""

import argparse
import shutil
import subprocess
import sys
from pathlib import Path


def run_command(cmd: list[str], cwd: Path = None) -> bool:
    """执行命令并返回是否成功"""
    try:
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(
            cmd, 
            cwd=cwd or Path.cwd(),
            check=True,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False


def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ["build", "dist", "*.egg-info"]
    for pattern in dirs_to_clean:
        for path in Path.cwd().glob(pattern):
            if path.is_dir():
                print(f"删除目录: {path}")
                shutil.rmtree(path)
            elif path.is_file():
                print(f"删除文件: {path}")
                path.unlink()


def build_wheel():
    """构建 wheel 包"""
    print("📦 构建 wheel 包...")
    
    cmd = ["uv", "build"]
    return run_command(cmd)


def check_wheel():
    """检查生成的 wheel 包"""
    print("🔍 检查 wheel 包...")
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist 目录不存在")
        return False
    
    wheel_files = list(dist_dir.glob("*.whl"))
    if not wheel_files:
        print("❌ 未找到 wheel 文件")
        return False
    
    print(f"✅ 找到 wheel 文件:")
    for wheel in wheel_files:
        print(f"  - {wheel.name} ({wheel.stat().st_size / 1024:.1f} KB)")
    
    return True


def publish_to_pypi(repository: str = "pypi", token: str = None):
    """发布到 PyPI"""
    print(f"🚀 发布到 {repository}...")
    
    cmd = ["uv", "publish"]
    
    if repository != "pypi":
        cmd.extend(["--repository", repository])
    
    if token:
        cmd.extend(["--token", token])
    
    return run_command(cmd)


def main():
    parser = argparse.ArgumentParser(
        description="AI中心解析流打包构建脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python build_package.py                     # 仅构建
  python build_package.py --publish           # 构建并发布到 PyPI
  python build_package.py --publish --test    # 发布到 TestPyPI
  python build_package.py --clean             # 清理后构建
        """
    )
    
    parser.add_argument(
        "--clean",
        action="store_true",
        help="构建前清理目录"
    )
    
    parser.add_argument(
        "--publish",
        action="store_true",
        help="构建后发布到 PyPI"
    )
    
    parser.add_argument(
        "--test",
        action="store_true",
        help="发布到 TestPyPI (需要配合 --publish)"
    )
    
    parser.add_argument(
        "--token",
        help="PyPI API token"
    )
    
    parser.add_argument(
        "--repository",
        help="自定义仓库 URL"
    )
    
    args = parser.parse_args()
    
    print("📦 AI中心解析流打包工具")
    print("=" * 50)
    
    # 清理构建目录
    if args.clean:
        clean_build()
    
    # 构建 wheel
    if not build_wheel():
        print("❌ 构建失败")
        sys.exit(1)
    
    # 检查构建结果
    if not check_wheel():
        print("❌ wheel 包检查失败")
        sys.exit(1)
    
    print("✅ 构建成功!")
    
    # 发布
    if args.publish:
        repository = "testpypi" if args.test else "pypi"
        if args.repository:
            repository = args.repository
            
        if publish_to_pypi(repository, args.token):
            print(f"✅ 发布到 {repository} 成功!")
        else:
            print(f"❌ 发布到 {repository} 失败")
            sys.exit(1)
    
    print("\n🎉 所有操作完成!")
    
    # 显示安装命令
    print("\n📝 安装命令:")
    if args.publish:
        if args.test:
            print("pip install --index-url https://test.pypi.org/simple/ aicenter-parserflow")
        else:
            print("pip install aicenter-parserflow")
    else:
        wheel_files = list(Path("dist").glob("*.whl"))
        if wheel_files:
            print(f"pip install {wheel_files[0]}")


if __name__ == "__main__":
    main()
