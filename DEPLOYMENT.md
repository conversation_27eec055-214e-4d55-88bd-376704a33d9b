# 🚀 杏仁解析部署指南

## 📋 部署概述

杏仁解析支持多种部署方式，从开发环境到生产环境的完整部署方案。

## 🎯 一键部署 (推荐)

### 快速启动

```bash
# 1. 克隆项目
git clone https://gitlab.bdo.com.cn/lxai/aicenter-parserflow.git
cd aicenter-parserflow

# 2. 一键部署
chmod +x bootstrap.sh
./bootstrap.sh

# 3. 访问服务
# Web管理界面: http://localhost:3000
# API文档: http://localhost:8010/docs
# MinerU API: http://localhost:8000/docs
```

## 🔧 分步部署

### 环境要求

- **操作系统**: Linux (Ubuntu 20.04+) / macOS / Windows
- **Python**: 3.9+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **GPU**: CUDA 11.8+ (可选，用于VLM加速)

### 1. 环境准备

```bash
# 安装 uv (现代Python包管理器)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装系统依赖 (Ubuntu)
sudo apt update
sudo apt install -y mysql-server redis-server

# 启动服务
sudo systemctl start mysql
sudo systemctl start redis
```

### 2. 数据库配置

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE almond_parser CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'almond'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON almond_parser.* TO 'almond'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. 核心服务部署

#### Almond Parser (核心服务)

```bash
cd almond_parser

# 安装依赖
uv sync

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等

# 初始化数据库
uv run python migrate.py init-db

# 启动服务
uv run python main.py
```

#### MinerU API (解析引擎)

```bash
cd mineru-api

# 安装依赖
uv sync

# Linux 环境 (支持 sglang)
uv sync --extra all

# Windows 环境 (不支持 sglang)
uv sync --extra core

# 启动服务
uv run python main.py
```

#### Web 管理界面

```bash
cd web

# 安装依赖
uv sync

# 启动服务
uv run python main.py
```

## 🐳 Docker 部署

### Docker Compose (推荐)

```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: almond_parser
      MYSQL_USER: almond
      MYSQL_PASSWORD: almondpassword
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"

  almond-parser:
    build: ./almond_parser
    ports:
      - "8010:8010"
    depends_on:
      - mysql
      - redis
    environment:
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis

  mineru-api:
    build: ./mineru-api
    ports:
      - "8000:8000"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  web:
    build: ./web
    ports:
      - "3000:3000"
    depends_on:
      - almond-parser

volumes:
  mysql_data:
```

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## ☁️ 生产环境部署

### 系统服务配置

#### Almond Parser 服务

```ini
# /etc/systemd/system/almond-parser.service
[Unit]
Description=Almond Parser Service
After=network.target mysql.service redis.service

[Service]
Type=simple
User=almond
WorkingDirectory=/opt/almond-parser
Environment=PATH=/opt/almond-parser/.venv/bin
ExecStart=/opt/almond-parser/.venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### MinerU API 服务

```ini
# /etc/systemd/system/mineru-api.service
[Unit]
Description=MinerU API Service
After=network.target

[Service]
Type=simple
User=almond
WorkingDirectory=/opt/mineru-api
Environment=PATH=/opt/mineru-api/.venv/bin
Environment=CUDA_VISIBLE_DEVICES=0,1
ExecStart=/opt/mineru-api/.venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### Web 服务

```ini
# /etc/systemd/system/almond-web.service
[Unit]
Description=Almond Web Service
After=network.target

[Service]
Type=simple
User=almond
WorkingDirectory=/opt/almond-web
Environment=PATH=/opt/almond-web/.venv/bin
ExecStart=/opt/almond-web/.venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 启动服务

```bash
# 启用并启动服务
sudo systemctl enable almond-parser mineru-api almond-web
sudo systemctl start almond-parser mineru-api almond-web

# 检查服务状态
sudo systemctl status almond-parser
sudo systemctl status mineru-api
sudo systemctl status almond-web
```

### Nginx 反向代理

```nginx
# /etc/nginx/sites-available/almond-parser
server {
    listen 80;
    server_name your-domain.com;

    # Web 管理界面
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API 接口
    location /api/ {
        proxy_pass http://localhost:8010;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # MinerU API
    location /mineru/ {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 监控和日志

### 日志配置

```python
# 日志配置示例
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "[{time:YYYY-MM-DD HH:mm:ss}] {level} | {name}:{function}:{line} - {message}",
            "style": "{"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "default"
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "/var/log/almond-parser/app.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "formatter": "default"
        }
    },
    "root": {
        "level": "INFO",
        "handlers": ["console", "file"]
    }
}
```

### 健康检查

```bash
# 服务健康检查脚本
#!/bin/bash
# health_check.sh

check_service() {
    local service_name=$1
    local url=$2
    
    if curl -f -s $url > /dev/null; then
        echo "✅ $service_name is healthy"
        return 0
    else
        echo "❌ $service_name is unhealthy"
        return 1
    fi
}

# 检查各个服务
check_service "Almond Parser" "http://localhost:8010/health"
check_service "MinerU API" "http://localhost:8000/health"
check_service "Web Interface" "http://localhost:3000/health"

# 检查数据库连接
mysql -u almond -p -e "SELECT 1" almond_parser > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ MySQL is healthy"
else
    echo "❌ MySQL is unhealthy"
fi

# 检查Redis连接
redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Redis is healthy"
else
    echo "❌ Redis is unhealthy"
fi
```

## 🔧 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   journalctl -u almond-parser -f
   
   # 检查端口占用
   netstat -tlnp | grep :8010
   ```

2. **数据库连接失败**
   ```bash
   # 测试数据库连接
   mysql -u almond -p -h localhost almond_parser
   
   # 检查数据库状态
   sudo systemctl status mysql
   ```

3. **GPU不可用**
   ```bash
   # 检查GPU状态
   nvidia-smi
   
   # 检查CUDA版本
   nvcc --version
   ```

4. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 调整服务配置
   # 减少 max_concurrent_tasks
   ```

### 性能优化

1. **数据库优化**
   ```sql
   -- 添加索引
   CREATE INDEX idx_document_status ON documents(status);
   CREATE INDEX idx_document_created_at ON documents(created_at);
   ```

2. **Redis优化**
   ```bash
   # 调整Redis配置
   echo "maxmemory 2gb" >> /etc/redis/redis.conf
   echo "maxmemory-policy allkeys-lru" >> /etc/redis/redis.conf
   ```

3. **系统优化**
   ```bash
   # 调整文件描述符限制
   echo "* soft nofile 65536" >> /etc/security/limits.conf
   echo "* hard nofile 65536" >> /etc/security/limits.conf
   ```

## 📈 扩展部署

### 多节点部署

```bash
# 节点1: 主服务 + Web
# 节点2: MinerU API (GPU)
# 节点3: MinerU API (CPU)
# 节点4: 数据库 + Redis

# 配置节点发现
export MINERU_NODES="http://node2:8000,http://node3:8000"
```

### 负载均衡

```nginx
upstream mineru_backend {
    server node2:8000 weight=3;  # GPU节点权重高
    server node3:8000 weight=1;  # CPU节点权重低
}

location /mineru/ {
    proxy_pass http://mineru_backend/;
}
```

---

**🚀 部署完成后，访问 Web 管理界面开始使用杏仁解析！**
