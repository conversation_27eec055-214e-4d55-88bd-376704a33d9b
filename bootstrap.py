#!/usr/bin/env python3
"""
AI中心解析流 - 环境安装引导脚本

支持选择性安装不同的服务组件：
- mineru-api: 仅安装 MinerU API 服务
- almond-parser: 仅安装杏仁解析服务  
- all: 安装所有服务
- dev: 开发环境（包含所有服务+开发工具）
"""

import argparse
import os
import platform
import subprocess
import sys
from pathlib import Path


def run_command(cmd: list[str], cwd: Path = None) -> bool:
    """执行命令并返回是否成功"""
    try:
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(
            cmd, 
            cwd=cwd or Path.cwd(),
            check=True,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False


def check_uv_installed() -> bool:
    """检查 uv 是否已安装"""
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def install_uv():
    """安装 uv"""
    print("正在安装 uv...")
    if platform.system() == "Windows":
        cmd = ["powershell", "-c", "irm https://astral.sh/uv/install.ps1 | iex"]
    else:
        cmd = ["curl", "-LsSf", "https://astral.sh/uv/install.sh", "|", "sh"]
    
    if not run_command(cmd):
        print("uv 安装失败，请手动安装: https://docs.astral.sh/uv/getting-started/installation/")
        sys.exit(1)


def get_platform_extras() -> list[str]:
    """获取平台相关的额外依赖"""
    if platform.system() == "Windows":
        return ["windows"]
    else:
        return ["linux"]


def install_service(service: str, include_dev: bool = False):
    """安装指定服务"""
    print(f"\n🚀 开始安装 {service} 环境...")
    
    # 构建安装命令
    extras = [service]
    
    # 添加平台依赖
    extras.extend(get_platform_extras())
    
    # 添加开发依赖
    if include_dev:
        extras.append("dev")
    
    # 构建 uv sync 命令
    cmd = ["uv", "sync"]
    for extra in extras:
        cmd.extend(["--extra", extra])
    
    if run_command(cmd):
        print(f"✅ {service} 环境安装成功!")
        return True
    else:
        print(f"❌ {service} 环境安装失败!")
        return False


def create_env_info_file(service: str):
    """创建环境信息文件，记录当前安装的服务"""
    env_info = {
        "service": service,
        "platform": platform.system().lower(),
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}",
    }
    
    with open(".env_info", "w") as f:
        for key, value in env_info.items():
            f.write(f"{key}={value}\n")


def main():
    parser = argparse.ArgumentParser(
        description="AI中心解析流环境安装脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python bootstrap.py --service mineru-api     # 仅安装 MinerU API
  python bootstrap.py --service almond-parser  # 仅安装杏仁解析服务
  python bootstrap.py --service all            # 安装所有服务
  python bootstrap.py --service all --dev      # 安装所有服务+开发工具
        """
    )
    
    parser.add_argument(
        "--service",
        choices=["mineru-api", "almond-parser", "all"],
        required=True,
        help="选择要安装的服务"
    )
    
    parser.add_argument(
        "--dev",
        action="store_true",
        help="包含开发依赖"
    )
    
    parser.add_argument(
        "--skip-uv-check",
        action="store_true", 
        help="跳过 uv 安装检查"
    )
    
    args = parser.parse_args()
    
    print("🎯 AI中心解析流环境安装向导")
    print("=" * 50)
    
    # 检查 uv 是否安装
    if not args.skip_uv_check and not check_uv_installed():
        print("❌ 未检测到 uv，正在安装...")
        install_uv()
    
    # 安装服务
    success = install_service(args.service, args.dev)
    
    if success:
        # 创建环境信息文件
        create_env_info_file(args.service)
        
        print("\n🎉 安装完成!")
        print(f"📝 已安装服务: {args.service}")
        print(f"🖥️  平台: {platform.system()}")
        
        print("\n📚 下一步:")
        print("1. 启动服务: python start_services.py")
        print("2. 查看文档: cat README.md")
        
        if args.service in ["almond-parser", "all"]:
            print("3. 配置数据库: 编辑 almond_parser/config.py")
        
    else:
        print("\n❌ 安装失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
