#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
API测试脚本 - 测试认证和文档列表API
"""

import asyncio
import aiohttp
import json
from datetime import datetime


async def test_api():
    """测试API接口"""
    base_url = "http://localhost:8010/api/v1"
    
    async with aiohttp.ClientSession() as session:
        try:
            # 1. 测试健康检查
            print("🔍 测试健康检查...")
            async with session.get(f"{base_url.replace('/api/v1', '')}/health") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 健康检查成功: {data}")
                else:
                    print(f"❌ 健康检查失败: {resp.status}")
                    return
            
            # 2. 测试登录
            print("\n🔐 测试登录...")
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            
            async with session.post(f"{base_url}/manage/login", json=login_data) as resp:
                if resp.status == 200:
                    login_result = await resp.json()
                    token = login_result.get("access_token")
                    print(f"✅ 登录成功，获取token: {token[:50]}...")
                else:
                    print(f"❌ 登录失败: {resp.status}")
                    text = await resp.text()
                    print(f"错误信息: {text}")
                    return
            
            # 3. 测试文档列表API
            print("\n📋 测试文档列表API...")
            headers = {"Authorization": f"Bearer {token}"}
            
            async with session.get(f"{base_url}/document/documents", headers=headers) as resp:
                if resp.status == 200:
                    docs_result = await resp.json()
                    print(f"✅ 文档列表获取成功:")
                    print(f"  - 总数: {docs_result.get('pagination', {}).get('total', 0)}")
                    print(f"  - 当前页文档数: {len(docs_result.get('items', []))}")
                else:
                    print(f"❌ 文档列表获取失败: {resp.status}")
                    text = await resp.text()
                    print(f"错误信息: {text}")
            
            # 4. 测试WebSocket连接
            print("\n🔌 测试WebSocket连接...")
            ws_url = f"ws://localhost:8010/api/v1/manage/ws/logs/document/test?token={token}"
            
            try:
                async with session.ws_connect(ws_url) as ws:
                    print("✅ WebSocket连接成功")
                    
                    # 发送ping消息
                    await ws.send_str(json.dumps({"type": "ping"}))
                    
                    # 等待响应
                    async for msg in ws:
                        if msg.type == aiohttp.WSMsgType.TEXT:
                            data = json.loads(msg.data)
                            print(f"📨 收到WebSocket消息: {data}")
                            if data.get("type") == "pong":
                                print("✅ WebSocket心跳测试成功")
                                break
                        elif msg.type == aiohttp.WSMsgType.ERROR:
                            print(f"❌ WebSocket错误: {ws.exception()}")
                            break
                        else:
                            break
                            
            except Exception as e:
                print(f"❌ WebSocket连接失败: {e}")
            
            print("\n🎉 API测试完成")
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_api())
