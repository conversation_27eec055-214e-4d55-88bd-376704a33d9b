#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
检查数据库中的node_id数据情况
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy import select, func, text
from almond_parser.db import get_async_session
from almond_parser.db.models.document import Document
from almond_parser.db.models.mineru_node import MinerUNode


async def check_node_data():
    """检查节点数据情况"""
    print("🔍 检查数据库中的节点数据情况...")
    
    async with get_async_session() as db:
        try:
            # 1. 检查documents表中node_id的分布
            print("\n📊 Documents表中node_id分布:")
            result = await db.execute(
                select(
                    Document.node_id,
                    func.count(Document.id).label('count'),
                    func.group_concat(Document.status.distinct()).label('statuses')
                )
                .group_by(Document.node_id)
                .order_by(func.count(Document.id).desc())
            )
            
            node_stats = result.fetchall()
            
            if not node_stats:
                print("❌ documents表中没有数据")
                return
            
            total_docs = sum(row.count for row in node_stats)
            print(f"📋 总文档数: {total_docs}")
            
            for row in node_stats:
                node_id = row.node_id if row.node_id else "NULL"
                percentage = (row.count / total_docs) * 100
                print(f"  - node_id={node_id}: {row.count}个文档 ({percentage:.1f}%) - 状态: {row.statuses}")
            
            # 2. 检查mineru_nodes表
            print("\n🖥️  MinerU节点表:")
            result = await db.execute(
                select(MinerUNode.id, MinerUNode.name, MinerUNode.status, MinerUNode.service_type)
                .order_by(MinerUNode.id)
            )
            
            nodes = result.fetchall()
            
            if not nodes:
                print("❌ mineru_nodes表中没有节点数据")
            else:
                print(f"📋 节点总数: {len(nodes)}")
                for node in nodes:
                    print(f"  - ID={node.id}: {node.name} ({node.status}) - {node.service_type}")
            
            # 3. 检查node_id关联情况
            print("\n🔗 节点关联情况:")
            result = await db.execute(
                select(
                    Document.node_id,
                    MinerUNode.name,
                    func.count(Document.id).label('doc_count')
                )
                .outerjoin(MinerUNode, Document.node_id == MinerUNode.id)
                .where(Document.node_id.isnot(None))
                .group_by(Document.node_id, MinerUNode.name)
                .order_by(func.count(Document.id).desc())
            )
            
            associations = result.fetchall()
            
            if not associations:
                print("❌ 没有找到有效的节点关联")
            else:
                for assoc in associations:
                    node_name = assoc.name if assoc.name else f"未知节点(ID={assoc.node_id})"
                    print(f"  - {node_name}: {assoc.doc_count}个文档")
            
            # 4. 检查最近的文档状态
            print("\n📄 最近10个文档的节点分配情况:")
            result = await db.execute(
                select(
                    Document.document_id,
                    Document.file_name,
                    Document.status,
                    Document.node_id,
                    Document.created_at,
                    Document.completed_at
                )
                .order_by(Document.created_at.desc())
                .limit(10)
            )
            
            recent_docs = result.fetchall()
            
            for doc in recent_docs:
                node_info = f"node_id={doc.node_id}" if doc.node_id else "未分配"
                duration = ""
                if doc.completed_at and doc.created_at:
                    diff = doc.completed_at - doc.created_at
                    duration = f" (耗时: {diff})"
                elif doc.created_at:
                    from datetime import datetime
                    diff = datetime.now() - doc.created_at
                    duration = f" (已运行: {diff})"
                
                print(f"  - {doc.file_name[:20]}... | {doc.status} | {node_info}{duration}")
                
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    await check_node_data()


if __name__ == "__main__":
    asyncio.run(main())
