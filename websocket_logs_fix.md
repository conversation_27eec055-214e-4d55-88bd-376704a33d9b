# WebSocket 日志连接修复

## 问题分析

用户反馈点击文档日志按钮后没有反应，WebSocket连接未能正常建立。

## 问题原因

1. **WebSocket URL问题**：
   - 使用硬编码的 `localhost:8010`，在不同环境下可能无法访问
   - 应该使用动态的 `window.location.host`

2. **消息格式不匹配**：
   - 后端发送JSON格式的结构化消息
   - 前端直接把消息当作纯文本处理
   - 导致日志显示格式错误

3. **日志级别识别问题**：
   - `getLogClass` 函数只能识别包含级别关键字的文本
   - 无法处理直接传入的日志级别（如 "ERROR", "INFO"）

## 修复内容

### 1. 修复WebSocket URL

**文件**: `web/src/api/document.ts`

```typescript
export function createLogWebSocket(documentId: string): WebSocket {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const host = window.location.host || 'localhost:8010'  // 使用动态host

  // 获取认证token
  const token = localStorage.getItem('token')
  const wsUrl = `${protocol}//${host}/api/v1/manage/ws/logs/document/${documentId}${token ? `?token=${token}` : ''}`

  console.log('WebSocket URL:', wsUrl)
  return new WebSocket(wsUrl)
}
```

**改进**：
- 使用 `window.location.host` 替代硬编码的 `localhost:8010`
- 支持不同环境下的动态URL生成

### 2. 修复消息解析逻辑

**文件**: `web/src/components/document/DocumentLogs.vue`

#### 2.1 支持JSON消息解析
```typescript
ws.onmessage = (event: MessageEvent) => {
  try {
    const data = JSON.parse(event.data)
    console.log('WebSocket消息:', data)
    
    if (data.type === 'connected') {
      // 连接成功消息
      logLines.value.push({
        text: data.message,
        class: 'log-info'
      })
    } else if (data.type === 'history') {
      // 历史日志
      data.logs.forEach((log: any) => {
        const logText = `[${log.timestamp}] [${log.level}] ${log.message}${log.source ? ` (${log.source})` : ''}`
        logLines.value.push({
          text: logText,
          class: getLogClass(log.level)
        })
      })
    } else if (data.type === 'log') {
      // 实时日志
      const logText = `[${new Date(data.timestamp * 1000).toISOString()}] [${data.level}] ${data.message}${data.source ? ` (${data.source})` : ''}`
      logLines.value.push({
        text: logText,
        class: getLogClass(data.level)
      })
    }
  } catch (error) {
    // 兼容纯文本消息
    logLines.value.push({
      text: event.data,
      class: getLogClass(event.data)
    })
  }
}
```

#### 2.2 改进日志级别识别
```typescript
const getLogClass = (line: string) => {
  const upperLine = line.toUpperCase()
  if (upperLine.includes('ERROR') || upperLine === 'ERROR') return 'log-error'
  if (upperLine.includes('WARNING') || upperLine === 'WARNING' || upperLine === 'WARN') return 'log-warning'
  if (upperLine.includes('DEBUG') || upperLine === 'DEBUG') return 'log-debug'
  if (upperLine.includes('INFO') || upperLine === 'INFO') return 'log-info'
  return 'log-info'  // 默认样式
}
```

## 后端WebSocket接口

### 接口路径
```
ws://host:port/api/v1/manage/ws/logs/document/{document_id}?token={jwt_token}
```

### 消息格式

#### 1. 连接成功消息
```json
{
  "type": "connected",
  "message": "已连接到文档 {document_id} 的日志流",
  "document_id": "document_id",
  "timestamp": 1641234567.123
}
```

#### 2. 历史日志消息
```json
{
  "type": "history",
  "logs": [
    {
      "timestamp": "2025-01-07T12:00:00",
      "level": "INFO",
      "message": "开始处理文档",
      "source": "parser"
    }
  ]
}
```

#### 3. 实时日志消息
```json
{
  "type": "log",
  "document_id": "document_id",
  "timestamp": 1641234567.123,
  "level": "INFO",
  "message": "处理进度: 50%",
  "source": "parser"
}
```

## 用户体验改进

### 修复前
1. 点击"查看日志"按钮
2. 对话框打开但没有内容
3. WebSocket连接失败，无错误提示
4. 用户不知道发生了什么

### 修复后
1. 点击"查看日志"按钮
2. 对话框打开，显示"WebSocket连接已建立"
3. 自动加载历史日志（最近50条）
4. 实时显示新的日志消息
5. 连接失败时显示明确的错误提示

## 错误处理

1. **连接失败**：显示"日志连接失败，请检查网络连接"
2. **认证失败**：WebSocket自动关闭，显示认证错误
3. **消息解析失败**：降级为纯文本显示，不影响功能
4. **网络断开**：显示"日志连接已断开，请重试"

## 测试建议

### 1. 连接测试
- 打开浏览器开发者工具的网络面板
- 点击文档的"查看日志"按钮
- 检查WebSocket连接是否成功建立
- 验证URL格式是否正确

### 2. 消息测试
- 验证连接成功后是否显示欢迎消息
- 检查是否加载了历史日志
- 触发新的解析任务，验证实时日志推送

### 3. 错误测试
- 断网情况下测试连接失败处理
- 无效token测试认证失败处理
- 不存在的文档ID测试权限验证

## 注意事项

1. **认证要求**：WebSocket连接需要有效的JWT token
2. **权限验证**：只能查看自己上传的文档日志
3. **连接管理**：对话框关闭时自动断开WebSocket连接
4. **性能考虑**：历史日志限制为最近50条，避免加载过多数据

这个修复确保了WebSocket日志功能能够正常工作，用户可以实时查看文档解析的详细日志信息。
