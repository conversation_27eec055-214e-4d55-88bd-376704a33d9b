# API Key 自动填充修复

## 问题分析

用户反馈前端文档上传时没有自动调用 `/api-keys/default` 接口来获取默认的API key，导致用户需要手动输入API key。

## 问题原因

1. **前端API函数缺失**：`web/src/api/apiKey.ts` 中没有 `getDefaultApiKey` 函数
2. **组件逻辑缺失**：`DocumentUpload.vue` 组件没有在对话框打开时自动获取默认API key的逻辑

## 修复内容

### 1. 添加获取默认API key的API函数

**文件**: `web/src/api/apiKey.ts`

```typescript
// 获取默认API密钥
export function getDefaultApiKey() {
  return request({
    url: '/api-keys/default',
    method: 'get'
  })
}
```

### 2. 修改文档上传组件，添加自动获取逻辑

**文件**: `web/src/components/document/DocumentUpload.vue`

#### 2.1 导入必要的函数
```typescript
import { ref, reactive, watch } from 'vue'  // 添加 watch
import { getDefaultApiKey } from '@/api/apiKey'  // 导入API函数
```

#### 2.2 添加监听器自动获取API key
```typescript
// 监听对话框打开，自动获取默认API key
watch(dialogVisible, async (newValue) => {
  if (newValue && !uploadForm.api_key) {
    try {
      const response = await getDefaultApiKey()
      if (response && response.key) {
        uploadForm.api_key = response.key
        ElMessage.success('已自动填入默认API Key')
      }
    } catch (error) {
      console.warn('获取默认API Key失败:', error)
      ElMessage.warning('无法获取默认API Key，请手动输入')
    }
  }
})
```

## 工作流程

### 修复后的流程
1. **用户点击"上传文档"按钮**
2. **对话框打开** (`dialogVisible = true`)
3. **watch监听器触发**，检查是否需要获取API key
4. **自动调用** `/api-keys/default` 接口
5. **后端逻辑**：
   - 查找用户的默认API key
   - 如果不存在，自动创建一个名为"默认 API Key"的永久密钥
   - 返回API key信息
6. **前端接收**：自动填入API key字段
7. **用户体验**：无需手动输入，可直接上传文档

### 后端接口逻辑 (已存在)
```python
@router.get("/default", response_model=ApiKeyResponse)
async def get_default_api_key(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取默认的API密钥（如果不存在则创建）"""
    service = ApiKeyService(db)
    api_key = await service.get_or_create_default_key(current_user["user_id"])
    return api_key
```

## 用户体验改进

### 修复前
1. 用户点击上传文档
2. 需要手动输入API key
3. 如果没有API key，需要先去API密钥页面创建
4. 复制API key回到上传页面
5. 才能开始上传

### 修复后
1. 用户点击上传文档
2. **自动填入默认API key** ✅
3. 直接开始上传文档
4. 如果获取失败，显示友好提示

## 错误处理

1. **网络错误**：显示"无法获取默认API Key，请手动输入"
2. **认证失败**：watch中捕获错误，不影响对话框正常打开
3. **API key已存在**：不会重复获取，保持用户已输入的值

## 测试建议

1. **正常流程测试**：
   - 清空API key字段
   - 点击上传文档按钮
   - 验证是否自动填入API key

2. **错误情况测试**：
   - 网络断开时测试
   - 未登录状态测试
   - API key字段已有值时测试

3. **用户体验测试**：
   - 验证提示消息是否友好
   - 验证不会影响正常的上传流程

## 注意事项

1. **只在API key为空时获取**：避免覆盖用户手动输入的值
2. **异步处理**：不阻塞对话框的打开
3. **错误静默处理**：获取失败不影响用户正常使用
4. **用户友好提示**：成功和失败都有相应的消息提示

这个修复确保了用户在上传文档时能够自动获取到默认的API key，大大改善了用户体验。
