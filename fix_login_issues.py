#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
登录问题修复脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from almond_parser.config import settings
from almond_parser.db import init_database, get_db
from almond_parser.db.models import User
from almond_parser.utils.auth import get_password_hash, verify_password, create_access_token
from sqlalchemy import select
from loguru import logger


async def fix_database_issues():
    """修复数据库相关问题"""
    try:
        logger.info("🔧 开始修复数据库问题...")
        
        # 初始化数据库
        await init_database()
        logger.info("✅ 数据库连接成功")
        
        async with get_db() as db:
            # 检查用户表
            result = await db.execute(select(User))
            users = result.scalars().all()
            
            if len(users) == 0:
                logger.info("📝 创建默认管理员用户...")
                
                # 创建默认管理员用户
                admin_user = User(
                    username="admin",
                    password=get_password_hash("admin123"),
                    is_admin=True,
                    is_active=True
                )
                
                db.add(admin_user)
                await db.commit()
                await db.refresh(admin_user)
                
                logger.info("✅ 创建默认管理员用户成功: admin / admin123")
            else:
                logger.info(f"📊 找到 {len(users)} 个用户:")
                for user in users:
                    logger.info(f"  - {user.username} (ID: {user.id}, 管理员: {user.is_admin}, 激活: {user.is_active})")
                
                # 检查admin用户
                admin_user = None
                for user in users:
                    if user.username == "admin":
                        admin_user = user
                        break
                
                if not admin_user:
                    logger.info("📝 创建admin用户...")
                    admin_user = User(
                        username="admin",
                        password=get_password_hash("admin123"),
                        is_admin=True,
                        is_active=True
                    )
                    db.add(admin_user)
                    await db.commit()
                    await db.refresh(admin_user)
                    logger.info("✅ 创建admin用户成功")
                else:
                    # 确保admin用户密码正确
                    if not verify_password("admin123", admin_user.password):
                        logger.info("🔧 重置admin用户密码...")
                        admin_user.password = get_password_hash("admin123")
                        await db.commit()
                        logger.info("✅ admin用户密码重置成功")
                    
                    # 确保admin用户是管理员且激活
                    if not admin_user.is_admin or not admin_user.is_active:
                        logger.info("🔧 修复admin用户权限...")
                        admin_user.is_admin = True
                        admin_user.is_active = True
                        await db.commit()
                        logger.info("✅ admin用户权限修复成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_login_flow():
    """测试登录流程"""
    try:
        logger.info("🧪 测试登录流程...")
        
        async with get_db() as db:
            # 查找admin用户
            result = await db.execute(select(User).where(User.username == "admin"))
            admin_user = result.scalar_one_or_none()
            
            if not admin_user:
                logger.error("❌ 找不到admin用户")
                return False
            
            # 测试密码验证
            if not verify_password("admin123", admin_user.password):
                logger.error("❌ admin用户密码验证失败")
                return False
            
            logger.info("✅ 密码验证成功")
            
            # 测试token创建
            token_data = {
                "sub": admin_user.username,
                "user_id": admin_user.id,
                "is_admin": admin_user.is_admin
            }
            
            token = create_access_token(token_data)
            logger.info(f"✅ Token创建成功: {token[:50]}...")
            
            # 测试token验证
            from almond_parser.utils.auth import verify_token
            payload = verify_token(token)
            
            if payload:
                logger.info(f"✅ Token验证成功: {payload}")
            else:
                logger.error("❌ Token验证失败")
                return False
        
        logger.info("🎉 登录流程测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 登录流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    logger.info("🚀 开始修复登录问题...")
    
    # 修复数据库问题
    db_fixed = await fix_database_issues()
    
    if not db_fixed:
        logger.error("❌ 数据库修复失败，无法继续")
        return False
    
    # 测试登录流程
    login_ok = await test_login_flow()
    
    if login_ok:
        logger.info("🎉 登录问题修复完成！")
        logger.info("📋 可以使用以下账号登录:")
        logger.info("  - 用户名: admin")
        logger.info("  - 密码: admin123")
        return True
    else:
        logger.error("❌ 登录流程仍有问题")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
