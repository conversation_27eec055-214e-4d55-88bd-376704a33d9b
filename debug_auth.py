#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
认证调试脚本 - 检查认证状态和token有效性
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from almond_parser.utils.auth import verify_token, create_access_token
from almond_parser.db import init_database, get_db
from almond_parser.db.models import User
from sqlalchemy import select
from loguru import logger


async def test_auth():
    """测试认证功能"""
    try:
        # 初始化数据库
        await init_database()
        logger.info("✅ 数据库连接成功")
        
        # 测试创建token
        test_payload = {"sub": "admin", "user_id": 1}
        test_token = create_access_token(test_payload)
        logger.info(f"✅ 创建测试token: {test_token[:50]}...")
        
        # 测试验证token
        payload = verify_token(test_token)
        if payload:
            logger.info(f"✅ Token验证成功: {payload}")
        else:
            logger.error("❌ Token验证失败")
        
        # 检查用户表
        async with get_db() as db:
            result = await db.execute(select(User))
            users = result.scalars().all()
            logger.info(f"📊 用户表中有 {len(users)} 个用户")
            
            for user in users:
                logger.info(f"  - 用户: {user.username}, ID: {user.id}, 管理员: {user.is_admin}")
        
        logger.info("🎉 认证系统检查完成")
        
    except Exception as e:
        logger.error(f"❌ 认证检查失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_auth())
