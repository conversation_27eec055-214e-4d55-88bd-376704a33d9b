"""
数据模型定义
"""
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
import uuid
from datetime import datetime


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class OCRRequest(BaseModel):
    """OCR 解析请求模型"""
    file_name: str = Field(..., description="文件名 (支持 .pdf, .doc, .docx, .ppt, .pptx, .xls, .xlsx, .jpg, .jpeg, .png)")
    file_content: bytes = Field(..., description="文件内容")
    lang: str = Field(default="ch", description="语言设置")
    backend: str = Field(default="pipeline", description="后端类型 (pipeline/vlm/sglang/vlm-sglang-client/vlm-transformers)")
    method: str = Field(default="auto", description="解析方法")
    callback_url: Optional[str] = Field(None, description="回调URL")
    callback_headers: Optional[Dict[str, str]] = Field(None, description="回调请求头")
    
    # 解析选项
    formula_enable: bool = Field(default=True, description="启用公式解析")
    table_enable: bool = Field(default=True, description="启用表格解析")
    start_page_id: int = Field(default=0, description="起始页码")
    end_page_id: Optional[int] = Field(None, description="结束页码")
    
    # 输出选项
    dump_md: bool = Field(default=True, description="输出markdown")
    dump_content_list: bool = Field(default=True, description="输出内容列表")
    dump_middle_json: bool = Field(default=True, description="输出中间JSON")


class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    message: str = Field(default="", description="状态消息")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class TaskStatusResponse(BaseModel):
    """任务状态查询响应"""
    task_id: str
    status: TaskStatus
    message: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class TaskResult(BaseModel):
    """任务结果模型"""
    task_id: str
    file_name: str
    output_dir: str
    files: Dict[str, str] = Field(default_factory=dict, description="生成的文件路径")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class CallbackData(BaseModel):
    """回调数据模型"""
    task_id: str
    status: TaskStatus
    result: Optional[TaskResult] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


def generate_task_id() -> str:
    """生成任务ID"""
    return str(uuid.uuid4())
