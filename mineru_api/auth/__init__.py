# -*- encoding: utf-8 -*-
"""
认证模块 - 提供API key认证功能
"""

from .middleware import AuthMiddleware
from .manager import AuthManager
from .models import APIKey, AuthConfig
from .backends import get_auth_backend


def verify_api_key(api_key: str = None) -> str:
    """
    简单的API key验证函数
    用于新增的任务状态查询接口
    """
    from fastapi import HTTPException, Header
    from ..config import ENABLE_AUTH

    if not ENABLE_AUTH:
        return "no-auth-required"

    if not api_key:
        raise HTTPException(status_code=401, detail="API key required")

    # 这里可以添加更复杂的验证逻辑
    # 目前简化处理，只要有key就通过
    return api_key


__all__ = [
    'AuthMiddleware',
    'AuthManager',
    'APIKey',
    'AuthConfig',
    'get_auth_backend',
    'verify_api_key'
]
