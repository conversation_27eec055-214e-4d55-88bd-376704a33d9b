[project]
name = "mineru-api"
version = "1.0.0"
description = "基于 LitServe 的 OCR 解析服务"
authors = [
    {name = "MineruAPI Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "litserve>=0.2.0",
    "pydantic>=2.0.0",
    "httpx>=0.25.0",
    "uvicorn>=0.24.0",
    "loguru>=0.7.0",
    "fastapi>=0.104.0",
    "python-multipart>=0.0.6",
    "Pillow>=10.0.0",
    "psutil>=5.9.0",
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
windows = [
    "comtypes>=1.1.14",
    "mineru[core]>=2.0.6",
]
linux = [
    "mineru[all]>=2.0.6",
]


[tool.uv]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]
