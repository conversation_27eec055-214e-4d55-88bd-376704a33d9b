# -*- encoding: utf-8 -*-
"""
@File   :__init__.py.py
@Time   :2025/6/25 14:31
<AUTHOR>
"""
from typing import Optional

from fastapi.responses import FileResponse
from fastapi import HTTPException, UploadFile, File, Form, APIRouter, Depends
from loguru import logger

from mineru_api.models import (
    OCRRequest
)
from mineru_api.services.history_service import history_service
from mineru_api.services.mineru_server import api
from mineru_api.services.backend_manager import backend_manager

from mineru_api.services.task_manager import task_manager
from mineru_api.config import ENABLE_AUTH, OUTPUT_DIR

router = APIRouter(tags=["mineru_api"])

# 认证依赖
auth_dependency = None
if ENABLE_AUTH:
    from mineru_api.auth.manager import get_auth_manager
    from mineru_api.auth.middleware import create_auth_dependency

    try:
        auth_manager = get_auth_manager()
        auth_dependency = create_auth_dependency(auth_manager)
    except RuntimeError:
        # 认证管理器未初始化，将在运行时处理
        pass



@router.post("/parse/upload")
async def parse_upload(
        file: UploadFile = File(...),
        lang: str = Form(default="ch"),
        backend: str = Form(default="pipeline"),
        method: str = Form(default="auto"),
        callback_url: Optional[str] = Form(default=None),
        formula_enable: bool = Form(default=True),
        table_enable: bool = Form(default=True),
        dump_md: bool = Form(default=True),
        dump_content_list: bool = Form(default=True),
        dump_middle_json: bool = Form(default=True),
        auth_result=Depends(auth_dependency) if ENABLE_AUTH and auth_dependency else None
):
    """文件上传解析接口"""
    try:
        # 读取文件内容
        file_content = await file.read()

        # 创建请求对象
        request = OCRRequest(
            file_name=file.filename,
            file_content=file_content,
            lang=lang,
            backend=backend,
            method=method,
            callback_url=callback_url,
            formula_enable=formula_enable,
            table_enable=table_enable,
            dump_md=dump_md,
            dump_content_list=dump_content_list,
            dump_middle_json=dump_middle_json
        )

        # 处理请求
        response = api.predict(request)
        return response.dict()

    except Exception as e:
        logger.error(f"文件上传解析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks")
async def list_tasks():
    """列出所有任务"""
    tasks = []
    for task_id, task in task_manager.tasks.items():
        tasks.append({
            "task_id": task_id,
            "status": task.status,
            "created_at": task.created_at,
            "message": task.message
        })
    return {"tasks": tasks}


@router.get("/backends")
async def get_backend_info():
    """获取后端配置信息"""
    return {
        "backend_info": backend_manager.get_backend_info(),
        "message": "后端配置信息"
    }


@router.get("/history")
async def get_task_history(
        status: Optional[str] = None,
        days: int = 7,
        limit: int = 100
):
    """查询任务历史"""
    from datetime import datetime, timedelta

    start_date = datetime.now() - timedelta(days=days)
    tasks = history_service.query_tasks(
        status=status,
        start_date=start_date,
        limit=limit
    )
    return {
        "tasks": tasks,
        "total": len(tasks),
        "period_days": days
    }


@router.get("/history/{task_id}")
async def get_single_task_history(task_id: str):
    """获取单个任务的详细历史"""
    task = history_service.get_task_history(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务历史不存在")
    return task


@router.get("/statistics")
async def get_statistics(days: int = 7):
    """获取统计信息"""
    stats = history_service.get_statistics(days)
    return stats


@router.post("/admin/cleanup")
async def cleanup_history(days: int = 30):
    """清理历史记录"""
    deleted_count = history_service.cleanup_old_records(days)
    return {
        "message": f"已清理 {deleted_count} 条历史记录",
        "deleted_count": deleted_count
    }




@router.get("/files/{task_id}/{file_path:path}", summary="下载生成文件")
async def download_file(task_id: str, file_path: str):
    """
    下载生成文件
    :param task_id: 任务ID，对应输出子目录
    :param file_path: 相对于任务目录的文件路径
    """
    target_file = OUTPUT_DIR / task_id / file_path

    if not target_file.exists() or not target_file.is_file():
        raise HTTPException(status_code=404, detail="文件不存在")

    return FileResponse(
        path=target_file,
        filename=target_file.name,
        media_type="application/octet-stream"
    )
