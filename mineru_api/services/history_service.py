"""
任务历史服务 - 轻量级SQLite存储
"""
import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

from loguru import logger

from ..config import BASE_DIR
from ..models import TaskStatusResponse


class TaskHistoryService:
    """任务历史服务"""

    def __init__(self, db_path: Path = None):
        self.db_path = db_path or BASE_DIR / "data" / "task_history.db"
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        try:
            # 确保数据库目录存在
            self.db_path.parent.mkdir(parents=True, exist_ok=True)

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS task_history (
                        task_id TEXT PRIMARY KEY,
                        file_name TEXT,
                        status TEXT,
                        created_at TEXT,
                        started_at TEXT,
                        completed_at TEXT,
                        duration_seconds REAL,
                        error_message TEXT,
                        request_params TEXT,
                        result_summary TEXT,
                        trace_id TEXT
                    )
                """)

                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_created_at ON task_history(created_at)
                """)

                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_status ON task_history(status)
                """)

                logger.info(f"任务历史数据库初始化完成: {self.db_path}")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")

    def save_task(self, task: TaskStatusResponse, request_params: Dict = None,
                  trace_id: str = None):
        """保存任务记录"""
        try:
            duration = None
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()

            result_summary = None
            if task.result:
                # 提取关键信息作为摘要
                result_summary = json.dumps({
                    "files_count": len(task.result.get("files", {})),
                    "output_dir": task.result.get("output_dir", ""),
                    "file_types": list(task.result.get("files", {}).keys())
                }, ensure_ascii=False)

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO task_history 
                    (task_id, file_name, status, created_at, started_at, completed_at,
                     duration_seconds, error_message, request_params, result_summary, trace_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task.task_id,
                    request_params.get("file_name", "") if request_params else "",
                    task.status.value,
                    task.created_at.isoformat(),
                    task.started_at.isoformat() if task.started_at else None,
                    task.completed_at.isoformat() if task.completed_at else None,
                    duration,
                    task.error,
                    json.dumps(request_params, ensure_ascii=False) if request_params else None,
                    result_summary,
                    trace_id
                ))

        except Exception as e:
            logger.error(f"保存任务历史失败: {e}")

    def get_task_history(self, task_id: str) -> Optional[Dict]:
        """获取单个任务历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM task_history WHERE task_id = ?",
                    (task_id,)
                )
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"查询任务历史失败: {e}")
            return None

    def query_tasks(self,
                    status: Optional[str] = None,
                    start_date: Optional[datetime] = None,
                    end_date: Optional[datetime] = None,
                    limit: int = 100) -> List[Dict]:
        """查询任务历史"""
        try:
            conditions = []
            params = []

            if status:
                conditions.append("status = ?")
                params.append(status)

            if start_date:
                conditions.append("created_at >= ?")
                params.append(start_date.isoformat())

            if end_date:
                conditions.append("created_at <= ?")
                params.append(end_date.isoformat())

            where_clause = " AND ".join(conditions) if conditions else "1=1"

            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(f"""
                    SELECT * FROM task_history 
                    WHERE {where_clause}
                    ORDER BY created_at DESC 
                    LIMIT ?
                """, params + [limit])

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"查询任务历史失败: {e}")
            return []

    def get_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            start_date = datetime.now() - timedelta(days=days)

            with sqlite3.connect(self.db_path) as conn:
                # 总体统计
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_tasks,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_tasks,
                        AVG(duration_seconds) as avg_duration,
                        MAX(duration_seconds) as max_duration
                    FROM task_history 
                    WHERE created_at >= ?
                """, (start_date.isoformat(),))

                stats = dict(cursor.fetchone())

                # 每日统计
                cursor = conn.execute("""
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as count,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
                    FROM task_history 
                    WHERE created_at >= ?
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC
                """, (start_date.isoformat(),))

                daily_stats = [dict(row) for row in cursor.fetchall()]

                return {
                    "period_days": days,
                    "summary": stats,
                    "daily_breakdown": daily_stats
                }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def cleanup_old_records(self, days: int = 30):
        """清理旧记录"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "DELETE FROM task_history WHERE created_at < ?",
                    (cutoff_date.isoformat(),)
                )
                deleted_count = cursor.rowcount

            logger.info(f"清理了 {deleted_count} 条超过 {days} 天的历史记录")
            return deleted_count

        except Exception as e:
            logger.error(f"清理历史记录失败: {e}")
            return 0


# 全局历史服务实例
history_service = TaskHistoryService()
