# -*- encoding: utf-8 -*-
"""
后端管理器 - 在启动时确定支持的后端模式，避免运行时重复检测
"""
import platform
from typing import Dict, List, Optional, Tuple
from loguru import logger

from .sglang_manager import sglang_manager
from ..config import BACKEND_MAPPING, SGLANG_URL


class BackendManager:
    """后端管理器 - 管理支持的解析后端"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.supported_backends: List[str] = []
        self.sglang_available = False
        self.sglang_url = SGLANG_URL
        self._initialized = False
    
    async def initialize(self, check_sglang: bool = True, auto_start_sglang: bool = False, backend_mode: str = "auto") -> None:
        """
        初始化后端管理器

        Args:
            check_sglang: 是否检查sglang服务
            auto_start_sglang: 是否自动启动sglang服务
            backend_mode: 后端模式 (auto/pipeline-only/sglang-only)
        """
        if self._initialized:
            return

        logger.info(f"🔧 初始化后端管理器 (模式: {backend_mode})...")

        # 基础后端（所有平台都支持）
        self.supported_backends = ["pipeline"]

        # 根据backend_mode调整策略
        if backend_mode == "pipeline-only":
            logger.info("🔒 仅启用 pipeline 模式")
        elif backend_mode == "sglang-only":
            logger.info("🚀 仅启用 sglang 模式")
            # 强制检查sglang，不添加pipeline到支持列表
            self.supported_backends = []
            if self.platform == "linux":
                await self._check_sglang_support(auto_start_sglang)
                if not self.sglang_available:
                    logger.error("❌ sglang-only 模式下 sglang 服务不可用")
                    raise RuntimeError("sglang 服务不可用，无法使用 sglang-only 模式")
            else:
                logger.error("❌ sglang-only 模式仅支持 Linux 平台")
                raise RuntimeError("sglang-only 模式仅支持 Linux 平台")
        else:  # auto mode
            # 检查sglang支持（仅Linux）
            if self.platform == "linux" and check_sglang:
                await self._check_sglang_support(auto_start_sglang)
            else:
                logger.info(f"平台 {self.platform} 不支持sglang，跳过检查")

            # VLM transformers支持（所有平台）
            self.supported_backends.append("vlm-transformers")

        self._initialized = True

        # 显示支持的后端
        logger.info("📋 支持的后端模式:")
        for backend in self.supported_backends:
            if backend == "vlm-sglang-client":
                logger.info(f"  ✅ {backend}: 加速模式 (sglang服务: {self.sglang_url})")
            else:
                logger.info(f"  ✅ {backend}: 传统模式")
    
    async def _check_sglang_support(self, auto_start: bool = False) -> None:
        """检查sglang支持"""
        logger.info("检查 sglang 服务支持...")
        
        # 检查是否已运行
        if await sglang_manager.is_running():
            logger.info("✅ sglang 服务已运行")
            self.sglang_available = True
            self.supported_backends.extend(["vlm-sglang-client", "vlm", "sglang"])
            return
        
        # 如果需要自动启动
        if auto_start:
            logger.info("尝试启动 sglang 服务...")
            success = await sglang_manager.start_server()
            if success:
                logger.info("✅ sglang 服务启动成功")
                self.sglang_available = True
                self.supported_backends.extend(["vlm-sglang-client", "vlm", "sglang"])
            else:
                logger.warning("⚠️ sglang 服务启动失败，将仅支持 pipeline 模式")
        else:
            logger.info("sglang 服务未运行，将仅支持 pipeline 模式")
            logger.info("提示: 使用 --auto-sglang 参数可自动启动 sglang 服务")
    
    def get_optimal_backend(self, requested_backend: str) -> Tuple[str, Optional[str]]:
        """
        获取最优后端配置
        
        Args:
            requested_backend: 请求的后端类型
            
        Returns:
            (实际后端, server_url)
        """
        # 映射后端名称
        mapped_backend = BACKEND_MAPPING.get(requested_backend, requested_backend)
        
        # 检查是否支持
        if mapped_backend in self.supported_backends:
            # 如果是sglang相关后端，返回server_url
            if mapped_backend == "vlm-sglang-client":
                return mapped_backend, self.sglang_url
            else:
                return mapped_backend, None
        
        # 不支持的后端，使用回退策略
        logger.warning(f"请求的后端 '{requested_backend}' 不受支持，回退到 pipeline 模式")
        return "pipeline", None
    
    def is_backend_supported(self, backend: str) -> bool:
        """检查后端是否受支持"""
        mapped_backend = BACKEND_MAPPING.get(backend, backend)
        return mapped_backend in self.supported_backends
    
    def get_supported_backends(self) -> List[str]:
        """获取支持的后端列表"""
        return self.supported_backends.copy()
    
    def get_backend_info(self) -> Dict:
        """获取后端信息"""
        return {
            "platform": self.platform,
            "sglang_available": self.sglang_available,
            "sglang_url": self.sglang_url if self.sglang_available else None,
            "supported_backends": self.supported_backends,
            "backend_mapping": BACKEND_MAPPING
        }


# 全局后端管理器实例
backend_manager = BackendManager()
