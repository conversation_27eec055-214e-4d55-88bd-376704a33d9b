"""
sglang 服务管理器
"""
import asyncio
import subprocess
import time
import psutil
import httpx
import os
import re
from pathlib import Path
from typing import Optional, Dict, Any, List
from loguru import logger

from ..config import (
    SGLANG_HOST, SGLANG_PORT, SGLANG_URL, SGLANG_HEALTH_CHECK_TIMEOUT,
    CUDA_DEVICE_MODE, CUDA_VISIBLE_DEVICES, CUDA_AUTO_SELECT, SGLANG_MEM_FRACTION_STATIC
)
from ..utils.async_utils import StructuredLogger


class SglangManager:
    """sglang 服务管理器"""

    def __init__(self):
        self.host = SGLANG_HOST
        self.port = SGLANG_PORT
        self.url = SGLANG_URL
        self.pid_file = Path(".sglang_pid")
        self.log_file = Path("logs/sglang.log")
        self.process: Optional[subprocess.Popen] = None

        # 确保日志目录存在
        self.log_file.parent.mkdir(exist_ok=True)

    async def is_running(self) -> bool:
        """检查 sglang 服务是否运行"""
        try:
            async with httpx.AsyncClient(timeout=SGLANG_HEALTH_CHECK_TIMEOUT) as client:
                response = await client.get(f"{self.url}/health")
                return response.status_code == 200
        except Exception:
            return False

    def get_available_gpus(self) -> List[int]:
        """获取可用的 GPU 列表"""
        try:
            # 使用 nvidia-smi 检测 GPU
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=index,memory.used,memory.total", "--format=csv,noheader,nounits"],
                capture_output=True, text=True, timeout=10
            )

            if result.returncode != 0:
                logger.warning("无法检测 GPU，可能未安装 NVIDIA 驱动")
                return []

            available_gpus = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.split(', ')
                    if len(parts) >= 3:
                        gpu_id = int(parts[0])
                        memory_used = int(parts[1])
                        memory_total = int(parts[2])

                        # 如果显存使用率低于 10%，认为 GPU 可用
                        usage_ratio = memory_used / memory_total
                        if usage_ratio < 0.1:
                            available_gpus.append(gpu_id)
                        else:
                            logger.info(f"GPU {gpu_id} 使用率 {usage_ratio:.1%}，跳过")

            logger.info(f"检测到可用 GPU: {available_gpus}")
            return available_gpus

        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError) as e:
            logger.warning(f"GPU 检测失败: {e}")
            return []
        except Exception as e:
            logger.error(f"GPU 检测异常: {e}")
            return []

    def select_cuda_devices(self) -> str:
        """选择 CUDA 设备"""
        logger.info(f"CUDA 配置 - 模式: {CUDA_DEVICE_MODE}, 自动选择: {CUDA_AUTO_SELECT}, 手动设备: {CUDA_VISIBLE_DEVICES}")

        # 验证配置
        valid_modes = ["auto", "manual", "all"]
        if CUDA_DEVICE_MODE not in valid_modes:
            logger.warning(f"无效的 CUDA_DEVICE_MODE: {CUDA_DEVICE_MODE}，有效值: {valid_modes}，使用默认值 'auto'")
            effective_mode = "auto"
        else:
            effective_mode = CUDA_DEVICE_MODE

        if effective_mode == "manual":
            if CUDA_VISIBLE_DEVICES:
                logger.info(f"使用手动指定的 CUDA 设备: {CUDA_VISIBLE_DEVICES}")
                return CUDA_VISIBLE_DEVICES
            else:
                logger.warning("CUDA_DEVICE_MODE=manual 但 CUDA_VISIBLE_DEVICES 未设置，使用默认设备 0")
                return "0"

        elif effective_mode == "all":
            logger.info("使用所有 CUDA 设备")
            return ""  # 空字符串表示使用所有设备

        elif effective_mode == "auto":
            if CUDA_AUTO_SELECT:
                available_gpus = self.get_available_gpus()
                if available_gpus:
                    # 选择第一个可用的 GPU
                    selected_gpu = str(available_gpus[0])
                    logger.info(f"自动选择 CUDA 设备: {selected_gpu}")
                    return selected_gpu
                else:
                    logger.warning("未找到可用的 GPU，将使用默认设备")
                    return "0"  # 默认使用第一个 GPU
            else:
                logger.info("CUDA_AUTO_SELECT=false，使用默认 CUDA 设备: 0")
                return "0"

        else:
            # 默认使用第一个 GPU
            logger.info("使用默认 CUDA 设备: 0")
            return "0"

    def get_pid(self) -> Optional[int]:
        """获取 sglang 进程 PID"""
        if self.pid_file.exists():
            try:
                with open(self.pid_file, 'r') as f:
                    pid = int(f.read().strip())

                # 检查进程是否存在
                if psutil.pid_exists(pid):
                    proc = psutil.Process(pid)
                    if "mineru-sglang-server" in " ".join(proc.cmdline()):
                        return pid

                # PID 文件过期，删除
                self.pid_file.unlink()
            except (ValueError, psutil.NoSuchProcess, psutil.AccessDenied):
                if self.pid_file.exists():
                    self.pid_file.unlink()

        return None

    def save_pid(self, pid: int):
        """保存进程 PID"""
        with open(self.pid_file, 'w') as f:
            f.write(str(pid))

    def is_port_in_use(self) -> bool:
        """检查端口是否被占用"""
        for conn in psutil.net_connections():
            if conn.laddr.port == self.port and conn.status == psutil.CONN_LISTEN:
                return True
        return False

    async def start_server(self, force_restart: bool = False) -> bool:
        """启动 sglang 服务器"""
        logger.info(f"启动 sglang 服务器: {self.url}")

        # 检查是否已经运行
        if await self.is_running():
            if not force_restart:
                logger.info("sglang 服务器已在运行")
                return True
            else:
                logger.info("强制重启 sglang 服务器")
                await self.stop_server()
                await asyncio.sleep(2)

        # 检查端口占用
        if self.is_port_in_use():
            logger.warning(f"端口 {self.port} 被占用，尝试停止现有服务")
            await self.stop_server()
            await asyncio.sleep(2)

        try:
            # 选择 CUDA 设备
            cuda_devices = self.select_cuda_devices()

            # 启动命令
            cmd = [
                "mineru-sglang-server",
                "--port", str(self.port),
                "--host", self.host,
                "--mem-fraction-static", str(SGLANG_MEM_FRACTION_STATIC),
            ]

            # 设置环境变量
            env = os.environ.copy()
            if cuda_devices:
                env["CUDA_VISIBLE_DEVICES"] = cuda_devices
                logger.info(f"设置 CUDA_VISIBLE_DEVICES={cuda_devices}")

            logger.info(f"执行命令: {' '.join(cmd)}")

            # 启动进程
            with open(self.log_file, 'w') as log_f:
                self.process = subprocess.Popen(
                    cmd,
                    stdout=log_f,
                    stderr=subprocess.STDOUT,
                    start_new_session=True,
                    env=env
                )

            # 保存 PID
            self.save_pid(self.process.pid)

            logger.info(f"sglang 服务器已启动，PID: {self.process.pid}")

            # 等待服务启动
            for i in range(30):  # 最多等待30秒
                if await self.is_running():
                    logger.success(f"✅ sglang 服务器启动成功: {self.url}")

                    StructuredLogger.log_task_event(
                        "sglang_manager", "sglang_server_started",
                        pid=self.process.pid,
                        url=self.url,
                        startup_time=i + 1
                    )

                    return True

                await asyncio.sleep(1)

            # 启动超时
            logger.error("sglang 服务器启动超时")
            await self.stop_server()
            return False

        except FileNotFoundError:
            logger.error("mineru-sglang-server 命令未找到，请确保已安装 mineru[all]")
            return False
        except Exception as e:
            logger.error(f"启动 sglang 服务器失败: {e}")
            return False

    async def stop_server(self, force_kill: bool = False) -> bool:
        """停止 sglang 服务器"""
        logger.info(f"停止 sglang 服务器 (force_kill={force_kill})")

        if force_kill:
            # 使用健康监控器的强制杀死功能
            try:
                from .sglang_health_monitor import sglang_health_monitor
                kill_result = await sglang_health_monitor.force_kill_all_sglang_processes()

                # 清理 PID 文件
                if self.pid_file.exists():
                    self.pid_file.unlink()

                logger.success("✅ sglang 服务器已强制停止")
                return kill_result["total_killed"] > 0 or kill_result["remaining_processes"] == 0

            except Exception as e:
                logger.error(f"强制停止失败: {e}")
                return False

        stopped = False

        # 通过 PID 停止
        pid = self.get_pid()
        if pid:
            try:
                proc = psutil.Process(pid)
                proc.terminate()

                # 等待进程结束
                try:
                    proc.wait(timeout=5)
                    stopped = True
                except psutil.TimeoutExpired:
                    # 强制杀死
                    proc.kill()
                    proc.wait(timeout=2)
                    stopped = True

                logger.info(f"已停止进程 {pid}")

            except psutil.NoSuchProcess:
                logger.info(f"进程 {pid} 不存在")
                stopped = True
            except Exception as e:
                logger.error(f"停止进程 {pid} 失败: {e}")

        # 通过进程名停止
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline'] and any('mineru-sglang-server' in arg for arg in proc.info['cmdline']):
                        proc.terminate()
                        stopped = True
                        logger.info(f"已停止进程 {proc.info['pid']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            logger.error(f"通过进程名停止失败: {e}")

        # 清理 PID 文件
        if self.pid_file.exists():
            self.pid_file.unlink()

        if stopped:
            logger.success("✅ sglang 服务器已停止")

            StructuredLogger.log_task_event(
                "sglang_manager", "sglang_server_stopped"
            )

        return stopped

    async def get_status(self) -> Dict[str, Any]:
        """获取 sglang 服务器状态"""
        status = {
            "running": False,
            "pid": None,
            "port_in_use": self.is_port_in_use(),
            "health_check": False,
            "url": self.url,
            "cuda_config": {
                "mode": CUDA_DEVICE_MODE,
                "auto_select": CUDA_AUTO_SELECT,
                "manual_devices": CUDA_VISIBLE_DEVICES,
                "available_gpus": self.get_available_gpus(),
                "selected_devices": self.select_cuda_devices()
            }
        }

        # 检查进程
        pid = self.get_pid()
        if pid:
            status["pid"] = pid
            status["running"] = True

        # 健康检查
        status["health_check"] = await self.is_running()

        return status

    async def ensure_running(self) -> bool:
        """确保 sglang 服务器运行"""
        if await self.is_running():
            return True

        logger.info("sglang 服务器未运行，尝试启动...")
        return await self.start_server()

    async def restart_server(self, force_kill: bool = False) -> bool:
        """重启 sglang 服务器"""
        logger.info(f"重启 sglang 服务器 (force_kill={force_kill})")
        await self.stop_server(force_kill=force_kill)
        await asyncio.sleep(3)  # 增加等待时间确保清理完成
        return await self.start_server()

    async def force_restart_server(self) -> bool:
        """强制重启 sglang 服务器（杀死所有相关进程）"""
        logger.warning("🔥 强制重启 sglang 服务器")
        return await self.restart_server(force_kill=True)

    async def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态（集成健康监控器）"""
        try:
            from .sglang_health_monitor import sglang_health_monitor
            return await sglang_health_monitor.get_detailed_status()
        except Exception as e:
            logger.error(f"获取健康状态失败: {e}")
            return {"error": str(e), "basic_status": await self.get_status()}


# 全局 sglang 管理器实例
sglang_manager = SglangManager()
