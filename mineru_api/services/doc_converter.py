import subprocess
import sys
from pathlib import Path

from PIL import Image
from loguru import logger

from ..utils.async_utils import StructuredLogger, timing_decorator


class ConvertError(Exception):
    """文档转换异常"""
    pass


class DocConverter:
    """文档转换器 - 支持多种格式转换为PDF"""

    # 支持的文件格式
    OFFICE_FORMATS = {'.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'}
    IMAGE_FORMATS = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif'}
    PDF_FORMATS = {'.pdf'}

    @staticmethod
    def is_supported_format(file_path: Path) -> bool:
        """检查是否为支持的文件格式"""
        suffix = file_path.suffix.lower()
        return suffix in (DocConverter.OFFICE_FORMATS |
                         DocConverter.IMAGE_FORMATS |
                         DocConverter.PDF_FORMATS)

    @staticmethod
    def get_file_type(file_path: Path) -> str:
        """获取文件类型"""
        suffix = file_path.suffix.lower()
        if suffix in DocConverter.OFFICE_FORMATS:
            return "office"
        elif suffix in DocConverter.IMAGE_FORMATS:
            return "image"
        elif suffix in DocConverter.PDF_FORMATS:
            return "pdf"
        else:
            return "unknown"

    @staticmethod
    @timing_decorator("document_conversion")
    def convert_to_pdf(input_path: Path, output_dir: Path, task_id: str = None) -> Path:
        """将文档转换为PDF"""
        file_type = DocConverter.get_file_type(input_path)

        if task_id:
            StructuredLogger.log_task_event(
                task_id, "conversion_started",
                file_name=input_path.name,
                file_type=file_type,
                file_size=input_path.stat().st_size if input_path.exists() else 0
            )

        logger.info(f"开始转换文档: {input_path.name} ({file_type})")

        try:
            if file_type == "pdf":
                # 已经是PDF，直接复制
                return DocConverter._copy_pdf(input_path, output_dir)
            elif file_type == "image":
                # 图像转PDF
                return DocConverter._convert_image_to_pdf(input_path, output_dir, task_id)
            elif file_type == "office":
                # Office文档转PDF
                if sys.platform == "win32":
                    return DocConverter._convert_office_win32(input_path, output_dir, task_id)
                else:
                    return DocConverter._convert_office_linux(input_path, output_dir, task_id)
            else:
                raise ConvertError(f"不支持的文件格式: {input_path.suffix}")

        except Exception as e:
            if task_id:
                StructuredLogger.log_error(
                    task_id, e,
                    context={
                        "file_name": input_path.name,
                        "file_type": file_type,
                        "conversion_method": "office_win32" if sys.platform == "win32" else "office_linux"
                    }
                )
            raise

    @staticmethod
    def _copy_pdf(input_path: Path, output_dir: Path) -> Path:
        """复制PDF文件"""
        output_dir.mkdir(parents=True, exist_ok=True)
        output_path = output_dir / f"{input_path.stem}.pdf"

        with open(input_path, 'rb') as src, open(output_path, 'wb') as dst:
            dst.write(src.read())

        logger.info(f"PDF文件已复制: {output_path}")
        return output_path

    @staticmethod
    def _convert_image_to_pdf(input_path: Path, output_dir: Path, task_id: str = None) -> Path:
        """将图像转换为PDF"""
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            output_path = output_dir / f"{input_path.stem}.pdf"

            # 打开图像
            with Image.open(input_path) as img:
                # 转换为RGB模式（PDF需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 保存为PDF
                img.save(output_path, "PDF", resolution=100.0)

            if task_id:
                StructuredLogger.log_task_event(
                    task_id, "image_converted_to_pdf",
                    input_file=input_path.name,
                    output_file=output_path.name,
                    image_mode=img.mode if 'img' in locals() else 'unknown'
                )

            logger.info(f"图像已转换为PDF: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"图像转PDF失败: {e}")
            raise ConvertError(f"图像转PDF失败: {str(e)}")

    @staticmethod
    def _convert_office_win32(input_path: Path, output_dir: Path, task_id: str = None) -> Path:
        try:
            import comtypes.client

            output_dir.mkdir(parents=True, exist_ok=True)
            output_path = output_dir / f"{input_path.stem}.pdf"

            suffix = input_path.suffix.lower()

            if suffix in ['.doc', '.docx']:
                # Word文档
                word = comtypes.client.CreateObject('Word.Application')
                word.Visible = False
                doc = word.Documents.Open(str(input_path.absolute()))
                doc.SaveAs(str(output_path.absolute()), FileFormat=17)  # 17 = PDF
                doc.Close()
                word.Quit()

                if task_id:
                    StructuredLogger.log_task_event(
                        task_id, "office_converted_via_word",
                        input_file=input_path.name,
                        output_file=output_path.name
                    )

            elif suffix in ['.ppt', '.pptx']:
                # PowerPoint文档
                ppt = comtypes.client.CreateObject('PowerPoint.Application')
                presentation = ppt.Presentations.Open(str(input_path.absolute()))
                presentation.SaveAs(str(output_path.absolute()), 32)  # 32 = PDF
                presentation.Close()
                ppt.Quit()

                if task_id:
                    StructuredLogger.log_task_event(
                        task_id, "office_converted_via_powerpoint",
                        input_file=input_path.name,
                        output_file=output_path.name
                    )

            elif suffix in ['.xls', '.xlsx']:
                # Excel文档
                excel = comtypes.client.CreateObject('Excel.Application')
                excel.Visible = False
                wb = excel.Workbooks.Open(str(input_path.absolute()))
                wb.ExportAsFixedFormat(0, str(output_path.absolute()))  # 0 = PDF
                wb.Close()
                excel.Quit()

                if task_id:
                    StructuredLogger.log_task_event(
                        task_id, "office_converted_via_excel",
                        input_file=input_path.name,
                        output_file=output_path.name
                    )
            else:
                raise ConvertError(f"不支持的Office格式: {suffix}")

            logger.info(f"Office文档已转换为PDF: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Windows Office转换失败: {str(e)}")
            raise ConvertError(f"Windows Office转换失败: {str(e)}")

    @staticmethod
    def _convert_office_linux(input_path: Path, output_dir: Path, task_id: str = None) -> Path:
        try:
            output_dir.mkdir(parents=True, exist_ok=True)

            cmd = [
                'soffice',
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', str(output_dir),
                str(input_path)
            ]

            if task_id:
                StructuredLogger.log_task_event(
                    task_id, "libreoffice_conversion_started",
                    command=" ".join(cmd),
                    input_file=input_path.name
                )

            process = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=300  # 5分钟超时
            )

            if process.returncode != 0:
                error_msg = f"LibreOffice转换失败: {process.stderr}"
                logger.error(error_msg)
                raise ConvertError(error_msg)

            output_path = output_dir / f"{input_path.stem}.pdf"
            if not output_path.exists():
                raise ConvertError("PDF文件未生成")

            if task_id:
                StructuredLogger.log_task_event(
                    task_id, "libreoffice_conversion_completed",
                    input_file=input_path.name,
                    output_file=output_path.name,
                    stdout=process.stdout[:500] if process.stdout else ""
                )

            logger.info(f"LibreOffice转换完成: {output_path}")
            return output_path

        except subprocess.TimeoutExpired:
            error_msg = "LibreOffice转换超时"
            logger.error(error_msg)
            raise ConvertError(error_msg)
        except Exception as e:
            logger.error(f"Linux Office转换失败: {str(e)}")
            raise ConvertError(f"Linux Office转换失败: {str(e)}")


# 全局文档转换器实例
doc_converter = DocConverter()
