"""
回调服务 - 处理任务完成后的回调通知
"""
import asyncio
from typing import Optional, Dict
import threading

import httpx
from loguru import logger

from .task_manager import task_manager
from ..config import CALLBACK_TIMEOUT, CALLBACK_RETRY_TIMES, CALLBACK_RETRY_DELAY
from ..models import CallbackData


class CallbackService:
    """回调服务"""

    def __init__(self):
        self._client = None
        self._client_lock = threading.Lock()

    def _get_client(self) -> httpx.AsyncClient:
        """获取或创建HTTP客户端（线程安全）"""
        if self._client is None or self._client.is_closed:
            with self._client_lock:
                if self._client is None or self._client.is_closed:
                    self._client = httpx.AsyncClient(timeout=CALLBACK_TIMEOUT)
        return self._client

    async def send_callback(self, task_id: str, callback_url: str,
                           headers: Optional[Dict[str, str]] = None):
        """发送回调通知"""
        logger.info(f"准备发送回调通知: {task_id} -> {callback_url}")

        # 获取任务状态和结果
        task_status = task_manager.get_task_status(task_id)
        task_result = task_manager.get_task_result(task_id)

        if not task_status:
            logger.error(f"任务 {task_id} 不存在，无法发送回调")
            return

        # 构建回调数据
        callback_data = CallbackData(
            task_id=task_id,
            status=task_status.status,
            result=task_result,
            error=task_status.error
        )
        logger.info(f"回调数据：{callback_data.model_dump(mode='json')}")

        # 准备请求头
        request_headers = {
            "Content-Type": "application/json",
            "User-Agent": "MineruAPI/1.0"
        }
        if headers:
            request_headers.update(headers)

        # 重试机制
        for attempt in range(CALLBACK_RETRY_TIMES):
            client = None
            try:
                # 每次重试都获取新的客户端实例
                client = self._get_client()

                response = await client.post(
                    callback_url,
                    json=callback_data.model_dump(mode="json"),
                    headers=request_headers
                )

                if response.status_code == 200:
                    logger.info(f"回调通知发送成功: {task_id} (尝试 {attempt + 1})")
                    return
                else:
                    logger.warning(f"回调通知响应数据：{response.text}")
                    logger.warning(f"回调通知响应异常: {task_id}, 状态码: {response.status_code}")

            except Exception as e:
                logger.error(f"回调通知发送失败: {task_id} (尝试 {attempt + 1}): {e}")

                # 如果是连接相关错误，重置客户端
                if "closed" in str(e).lower() or "transport" in str(e).lower():
                    logger.warning(f"检测到连接问题，重置HTTP客户端")
                    with self._client_lock:
                        if self._client and not self._client.is_closed:
                            try:
                                await self._client.aclose()
                            except:
                                pass
                        self._client = None

            # 如果不是最后一次尝试，等待后重试
            if attempt < CALLBACK_RETRY_TIMES - 1:
                await asyncio.sleep(CALLBACK_RETRY_DELAY)

        logger.error(f"回调通知最终失败: {task_id}, 已尝试 {CALLBACK_RETRY_TIMES} 次")

    async def close(self):
        """关闭客户端"""
        with self._client_lock:
            if self._client and not self._client.is_closed:
                try:
                    await self._client.aclose()
                except Exception as e:
                    logger.warning(f"关闭HTTP客户端时出错: {e}")
                finally:
                    self._client = None


# 全局回调服务实例
callback_service = CallbackService()
