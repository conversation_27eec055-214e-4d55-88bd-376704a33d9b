"""
任务管理器 - 负责任务状态管理和队列处理
"""
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Dict, Optional

from loguru import logger

from .history_service import history_service
from ..config import MAX_CONCURRENT_TASKS, TASK_TIMEOUT
from ..models import TaskStatus, TaskStatusResponse, TaskResult, OCRRequest
from ..utils.async_utils import StructuredLogger, generate_trace_id


class TaskManager:
    """任务管理器"""

    def __init__(self):
        self.tasks: Dict[str, TaskStatusResponse] = {}
        self.results: Dict[str, TaskResult] = {}
        self.trace_ids: Dict[str, str] = {}  # 任务追踪ID
        self.request_params: Dict[str, Dict] = {}  # 请求参数
        self.executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_TASKS)
        self._lock = threading.Lock()

    def create_task(self, task_id: str, request: OCRRequest) -> TaskStatusResponse:
        """创建新任务"""
        with self._lock:
            # 生成追踪ID
            trace_id = generate_trace_id()
            self.trace_ids[task_id] = trace_id

            # 保存请求参数（用于历史记录）
            self.request_params[task_id] = {
                "file_name": request.file_name,
                "lang": request.lang,
                "backend": request.backend,
                "method": request.method,
                "file_size": len(request.file_content),
                "callback_url": request.callback_url,
                "formula_enable": request.formula_enable,
                "table_enable": request.table_enable
            }

            task = TaskStatusResponse(
                task_id=task_id,
                status=TaskStatus.PENDING,
                message="任务已创建，等待处理",
                created_at=datetime.now()
            )
            self.tasks[task_id] = task

            # 结构化日志记录
            StructuredLogger.log_task_event(
                task_id, "task_created",
                trace_id=trace_id,
                file_name=request.file_name,
                file_size=len(request.file_content),
                backend=request.backend,
                method=request.method,
                lang=request.lang
            )

            logger.info(f"创建任务: {task_id} (trace: {trace_id})")
            return task

    def get_task_status(self, task_id: str) -> Optional[TaskStatusResponse]:
        """获取任务状态"""
        with self._lock:
            return self.tasks.get(task_id)

    def update_task_status(self, task_id: str, status: TaskStatus,
                           message: str = "", error: str = None):
        """更新任务状态"""
        with self._lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                old_status = task.status
                task.status = status
                task.message = message

                if status == TaskStatus.PROCESSING and not task.started_at:
                    task.started_at = datetime.now()
                elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                    task.completed_at = datetime.now()

                if error:
                    task.error = error

                if status == TaskStatus.COMPLETED and task_id in self.results:
                    task.result = self.results[task_id].dict()

                # 结构化日志记录
                trace_id = self.trace_ids.get(task_id, "")
                StructuredLogger.log_task_event(
                    task_id, "status_changed",
                    trace_id=trace_id,
                    old_status=old_status.value,
                    new_status=status.value,
                    message=message,
                    error=error
                )

                # 保存到历史记录
                if status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                    request_params = self.request_params.get(task_id, {})
                    history_service.save_task(task, request_params, trace_id)

                logger.info(f"任务 {task_id} 状态更新: {old_status} -> {status} - {message}")

    def set_task_result(self, task_id: str, result: TaskResult):
        """设置任务结果"""
        with self._lock:
            self.results[task_id] = result
            logger.info(f"任务 {task_id} 结果已保存")

    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果"""
        with self._lock:
            return self.results.get(task_id)

    async def submit_task(self, task_id: str, ocr_service, request: OCRRequest,
                          callback_service=None):
        """提交任务到线程池执行"""
        try:
            def run_task():
                """在线程中运行OCR任务"""
                try:
                    self.update_task_status(task_id, TaskStatus.PROCESSING, "正在处理OCR解析")

                    # 创建新的事件循环来执行OCR解析
                    task_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(task_loop)
                    result = task_loop.run_until_complete(ocr_service.process_ocr(task_id, request))
                    task_loop.close()

                    # 保存结果
                    self.set_task_result(task_id, result)
                    self.update_task_status(task_id, TaskStatus.COMPLETED, "OCR解析完成")

                    # 触发回调
                    if callback_service and request.callback_url:
                        try:
                            # 使用新的回调处理器（线程安全）
                            from ..utils.callback_utils import callback_handler
                            callback_handler.send_callback_sync(
                                task_id,
                                request.callback_url,
                                request.callback_headers
                            )
                        except Exception as callback_error:
                            logger.error(f"回调执行失败: {callback_error}")

                    return result

                except Exception as e:
                    error_msg = f"OCR解析失败: {str(e)}"
                    logger.error(f"任务 {task_id} 执行失败: {e}")
                    self.update_task_status(task_id, TaskStatus.FAILED, error_msg, str(e))

                    # 失败时也触发回调
                    if callback_service and request.callback_url:
                        try:
                            # 使用新的回调处理器（线程安全）
                            from ..utils.callback_utils import callback_handler
                            callback_handler.send_callback_sync(
                                task_id,
                                request.callback_url,
                                request.callback_headers
                            )
                        except Exception as callback_error:
                            logger.error(f"回调失败: {callback_error}")
                    raise

            # 提交到线程池
            future = self.executor.submit(run_task)

            # 设置超时
            try:
                await asyncio.wait_for(
                    asyncio.wrap_future(future),
                    timeout=TASK_TIMEOUT
                )
            except asyncio.TimeoutError:
                self.update_task_status(task_id, TaskStatus.FAILED, "任务执行超时")
                logger.error(f"任务 {task_id} 执行超时")

        except Exception as e:
            logger.error(f"提交任务失败: {task_id}, 错误: {e}")
            self.update_task_status(task_id, TaskStatus.FAILED, f"提交任务失败: {str(e)}")
            raise

    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的旧任务"""
        with self._lock:
            current_time = datetime.now()
            to_remove = []

            for task_id, task in self.tasks.items():
                if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED] and
                        task.completed_at and
                        (current_time - task.completed_at).total_seconds() > max_age_hours * 3600):
                    to_remove.append(task_id)

            for task_id in to_remove:
                del self.tasks[task_id]
                if task_id in self.results:
                    del self.results[task_id]
                logger.info(f"清理过期任务: {task_id}")


# 全局任务管理器实例
task_manager = TaskManager()
