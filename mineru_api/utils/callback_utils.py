"""
回调工具类 - 提供线程安全的回调处理
"""
import asyncio
import threading
from typing import Optional, Dict
from concurrent.futures import ThreadPoolExecutor
from loguru import logger

from ..services.callback_service import callback_service


class CallbackHandler:
    """回调处理器 - 线程安全的回调执行"""
    
    def __init__(self):
        self._executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="callback")
        self._shutdown = False
    
    def send_callback_sync(self, task_id: str, callback_url: str, 
                          headers: Optional[Dict[str, str]] = None):
        """同步方式发送回调（在线程池中执行）"""
        if self._shutdown:
            logger.warning(f"回调处理器已关闭，跳过回调: {task_id}")
            return
            
        try:
            # 提交到线程池执行
            future = self._executor.submit(
                self._run_async_callback, 
                task_id, 
                callback_url, 
                headers
            )
            
            # 不等待结果，让回调在后台执行
            logger.info(f"回调任务已提交到线程池: {task_id}")
            
        except Exception as e:
            logger.error(f"提交回调任务失败: {task_id}, 错误: {e}")
    
    def _run_async_callback(self, task_id: str, callback_url: str, 
                           headers: Optional[Dict[str, str]] = None):
        """在独立线程中运行异步回调"""
        try:
            # 在新线程中创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 执行异步回调
                loop.run_until_complete(
                    callback_service.send_callback(task_id, callback_url, headers)
                )
                logger.info(f"回调执行完成: {task_id}")
                
            finally:
                # 确保循环被正确关闭
                try:
                    # 取消所有待处理的任务
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()
                    
                    # 等待所有任务完成或取消
                    if pending:
                        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                        
                finally:
                    loop.close()
                    
        except Exception as e:
            logger.error(f"回调线程执行失败: {task_id}, 错误: {e}")
    
    def shutdown(self):
        """关闭回调处理器"""
        logger.info("正在关闭回调处理器...")
        self._shutdown = True
        
        try:
            # 等待所有回调任务完成
            # 注意：移除timeout参数以确保兼容性
            self._executor.shutdown(wait=True)
            logger.info("回调处理器已关闭")
        except Exception as e:
            logger.error(f"关闭回调处理器时出错: {e}")


# 全局回调处理器实例
callback_handler = CallbackHandler()
