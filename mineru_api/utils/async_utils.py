"""
异步工具函数
"""
import asyncio
import functools
import uuid
import time
import traceback
from typing import Callable, Any, Dict, Optional
from loguru import logger
import json
from datetime import datetime


def generate_trace_id() -> str:
    """生成追踪ID"""
    return str(uuid.uuid4())[:8]


class StructuredLogger:
    """结构化日志记录器"""

    @staticmethod
    def log_task_event(task_id: str, event: str, **kwargs):
        """记录任务事件"""
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "task_id": task_id,
            "event": event,
            "trace_id": kwargs.get("trace_id", ""),
            **kwargs
        }
        logger.info(f"TASK_EVENT: {json.dumps(log_data, ensure_ascii=False)}")

    @staticmethod
    def log_error(task_id: str, error: Exception, context: Dict = None):
        """记录错误详情"""
        error_data = {
            "timestamp": datetime.now().isoformat(),
            "task_id": task_id,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "traceback": traceback.format_exc(),
            "context": context or {}
        }
        logger.error(f"TASK_ERROR: {json.dumps(error_data, ensure_ascii=False)}")

    @staticmethod
    def log_performance(task_id: str, operation: str, duration: float, **metrics):
        """记录性能指标"""
        perf_data = {
            "timestamp": datetime.now().isoformat(),
            "task_id": task_id,
            "operation": operation,
            "duration_seconds": round(duration, 3),
            "metrics": metrics
        }
        logger.info(f"TASK_PERFORMANCE: {json.dumps(perf_data, ensure_ascii=False)}")


def run_in_background(func: Callable) -> Callable:
    """装饰器：在后台运行异步函数"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        task = asyncio.create_task(func(*args, **kwargs))
        logger.info(f"后台任务已启动: {func.__name__}")
        return task
    return wrapper


async def safe_async_call(coro, error_msg: str = "异步调用失败"):
    """安全的异步调用，捕获异常"""
    try:
        return await coro
    except Exception as e:
        logger.error(f"{error_msg}: {e}")
        raise


def create_background_task(coro, name: str = None):
    """创建后台任务"""
    task = asyncio.create_task(coro)
    if name:
        task.set_name(name)

    def done_callback(task):
        if task.exception():
            logger.error(f"后台任务 {name or 'unnamed'} 执行失败: {task.exception()}")
        else:
            logger.info(f"后台任务 {name or 'unnamed'} 执行完成")

    task.add_done_callback(done_callback)
    return task


def timing_decorator(operation_name: str):
    """性能计时装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # 尝试从参数中获取task_id
                task_id = kwargs.get('task_id') or (args[0] if args else 'unknown')
                StructuredLogger.log_performance(
                    str(task_id), operation_name, duration
                )
                return result
            except Exception as e:
                duration = time.time() - start_time
                task_id = kwargs.get('task_id') or (args[0] if args else 'unknown')
                StructuredLogger.log_performance(
                    str(task_id), operation_name, duration, status="failed"
                )
                raise
        return wrapper
    return decorator
