"""
快速测试新增的回调可靠性功能
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        # 测试导入
        from mineru_api.services.callback_tracker import callback_tracker
        from mineru_api.services.task_manager import task_manager
        from mineru_api.models import OCRRequest, TaskStatus
        print("✅ 模块导入成功")
        
        # 测试回调跟踪
        task_id = "test-001"
        callback_url = "http://test.com/callback"
        callback_tracker.track_callback(task_id, callback_url)
        
        status = callback_tracker.get_callback_status(task_id)
        if status and status["status"] == "pending":
            print("✅ 回调跟踪功能正常")
        else:
            print("❌ 回调跟踪功能异常")
            return False
        
        # 测试任务管理
        request = OCRRequest(
            file_name="test.pdf",
            file_content=b"test content"
        )
        
        task = task_manager.create_task(task_id, request)
        task_status = task_manager.get_task_status(task_id)
        
        if task_status and task_status.task_id == task_id:
            print("✅ 任务管理功能正常")
        else:
            print("❌ 任务管理功能异常")
            return False
        
        print("🎉 基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("\n✅ 回调可靠性增强功能已就绪！")
    else:
        print("\n❌ 存在问题，请检查实现。")
