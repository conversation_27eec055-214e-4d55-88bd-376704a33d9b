#!/usr/bin/env python3
"""
验证回调可靠性增强功能的脚本
检查所有新增组件是否正常工作
"""
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def check_imports():
    """检查所有新增模块是否可以正常导入"""
    print("🔍 检查模块导入...")

    try:
        from mineru_api.services.callback_tracker import callback_tracker
        print("✅ callback_tracker 导入成功")

        from mineru_api.api.task_status import router
        print("✅ task_status API 导入成功")

        from mineru_api.utils.callback_enhancer import init_callback_enhancements
        print("✅ callback_enhancer 导入成功")

        from mineru_api.auth import verify_api_key
        print("✅ 认证模块导入成功")

        return True

    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def check_callback_tracker():
    """检查回调跟踪器功能"""
    print("\n🔍 检查回调跟踪器...")

    try:
        from mineru_api.services.callback_tracker import callback_tracker

        # 测试基本功能
        task_id = "verify-test-001"
        callback_url = "http://test.com/callback"

        # 开始跟踪
        callback_tracker.track_callback(task_id, callback_url)

        # 检查状态
        status = callback_tracker.get_callback_status(task_id)
        if status and status["status"] == "pending":
            print("✅ 回调跟踪功能正常")
            return True
        else:
            print("❌ 回调跟踪功能异常")
            return False

    except Exception as e:
        print(f"❌ 回调跟踪器检查失败: {e}")
        return False


def check_task_manager_integration():
    """检查任务管理器集成"""
    print("\n🔍 检查任务管理器集成...")

    try:
        from mineru_api.services.task_manager import task_manager
        from mineru_api.models import OCRRequest

        # 创建测试任务
        request = OCRRequest(
            file_name="verify_test.pdf",
            file_content=b"test content"
        )

        task_id = "verify-test-002"
        task = task_manager.create_task(task_id, request)

        # 检查任务状态
        status = task_manager.get_task_status(task_id)
        if status and status.task_id == task_id:
            print("✅ 任务管理器集成正常")
            return True
        else:
            print("❌ 任务管理器集成异常")
            return False

    except Exception as e:
        print(f"❌ 任务管理器集成检查失败: {e}")
        return False


def check_api_routes():
    """检查API路由定义"""
    print("\n🔍 检查API路由...")

    try:
        from mineru_api.api.task_status import router

        # 检查路由是否定义
        routes = [route.path for route in router.routes]
        expected_routes = [
            "/tasks/{task_id}/status",
            "/tasks/{task_id}/result",
            "/tasks/{task_id}/callback-status",
            "/tasks/{task_id}/retry-callback",
            "/tasks/stats",
            "/tasks/health"
        ]

        missing_routes = []
        for expected in expected_routes:
            if not any(expected in route for route in routes):
                missing_routes.append(expected)

        if not missing_routes:
            print("✅ API路由定义完整")
            return True
        else:
            print(f"❌ 缺少API路由: {missing_routes}")
            return False

    except Exception as e:
        print(f"❌ API路由检查失败: {e}")
        return False


def check_enhancement_initialization():
    """检查增强功能初始化"""
    print("\n🔍 检查增强功能初始化...")

    try:
        from mineru_api.utils.callback_enhancer import init_callback_enhancements, cleanup_callback_enhancements
        from mineru_api.services.callback_tracker import callback_tracker

        # 初始化增强功能
        init_callback_enhancements()

        # 检查重试工作线程是否启动
        if callback_tracker._running:
            print("✅ 重试工作线程已启动")

            # 清理
            cleanup_callback_enhancements()

            if not callback_tracker._running:
                print("✅ 增强功能清理正常")
                return True
            else:
                print("❌ 增强功能清理异常")
                return False
        else:
            print("❌ 重试工作线程未启动")
            return False

    except Exception as e:
        print(f"❌ 增强功能初始化检查失败: {e}")
        return False


def check_data_persistence():
    """检查数据持久化"""
    print("\n🔍 检查数据持久化...")

    try:
        from mineru_api.services.callback_tracker import callback_tracker

        # 创建测试数据
        task_id = "verify-test-003"
        callback_tracker.track_callback(task_id, "http://test.com/callback")

        # 检查存储文件
        storage_file = callback_tracker._storage_file
        if storage_file.exists():
            print(f"✅ 数据文件已创建: {storage_file}")

            # 检查文件内容
            import json
            with open(storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if task_id in data:
                print("✅ 数据持久化正常")
                return True
            else:
                print("❌ 数据未正确保存")
                return False
        else:
            print("❌ 数据文件未创建")
            return False

    except Exception as e:
        print(f"❌ 数据持久化检查失败: {e}")
        return False


def check_configuration():
    """检查配置项"""
    print("\n🔍 检查配置项...")

    try:
        from mineru_api.config import (
            CALLBACK_TIMEOUT, CALLBACK_RETRY_TIMES, CALLBACK_RETRY_DELAY,
            TASK_TIMEOUT, MAX_CONCURRENT_TASKS
        )

        configs = {
            "CALLBACK_TIMEOUT": CALLBACK_TIMEOUT,
            "CALLBACK_RETRY_TIMES": CALLBACK_RETRY_TIMES,
            "CALLBACK_RETRY_DELAY": CALLBACK_RETRY_DELAY,
            "TASK_TIMEOUT": TASK_TIMEOUT,
            "MAX_CONCURRENT_TASKS": MAX_CONCURRENT_TASKS
        }

        print("配置项值:")
        for key, value in configs.items():
            print(f"  {key}: {value}")

        print("✅ 配置项检查完成")
        return True

    except Exception as e:
        print(f"❌ 配置项检查失败: {e}")
        return False


def main():
    """主验证函数"""
    print("🚀 开始验证回调可靠性增强功能...\n")

    checks = [
        ("模块导入", check_imports),
        ("回调跟踪器", check_callback_tracker),
        ("任务管理器集成", check_task_manager_integration),
        ("API路由", check_api_routes),
        ("增强功能初始化", check_enhancement_initialization),
        ("数据持久化", check_data_persistence),
        ("配置项", check_configuration)
    ]

    results = []

    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 检查出错: {e}")
            results.append((name, False))

    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 验证结果汇总:")
    print("=" * 50)

    passed = 0
    total = len(results)

    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:20} {status}")
        if result:
            passed += 1

    print("=" * 50)
    print(f"总计: {passed}/{total} 项检查通过")

    if passed == total:
        print("🎉 所有检查都通过！回调可靠性增强功能已就绪。")
        return True
    else:
        print("⚠️  部分检查未通过，请检查相关问题。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
