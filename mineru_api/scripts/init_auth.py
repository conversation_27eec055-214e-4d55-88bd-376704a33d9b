#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
认证系统初始化脚本
用于创建第一个管理员API key
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import get_auth_config
from auth.manager import AuthManager
from auth.models import CreateAPIKeyRequest


async def init_auth():
    """初始化认证系统"""
    print("🔐 MineruAPI 认证系统初始化")
    print("=" * 50)
    
    try:
        # 初始化认证管理器
        config = get_auth_config()
        auth_manager = AuthManager(config)
        
        print(f"✅ 认证后端: {config.backend_type.value}")
        
        # 检查是否已有API key
        existing_keys = await auth_manager.list_api_keys()
        if existing_keys:
            print(f"⚠️  已存在 {len(existing_keys)} 个API key")
            
            # 显示现有key
            for key_info in existing_keys[:3]:  # 只显示前3个
                print(f"   - {key_info.name} ({key_info.key_prefix})")
            
            if len(existing_keys) > 3:
                print(f"   ... 还有 {len(existing_keys) - 3} 个")
            
            choice = input("\n是否继续创建新的API key? (y/N): ").lower()
            if choice != 'y':
                print("操作已取消")
                return
        
        # 获取用户输入
        print("\n📝 创建管理员API key")
        name = input("请输入API key名称 (默认: Admin): ").strip() or "Admin"
        
        expires_input = input("请输入过期天数 (默认: 永不过期): ").strip()
        expires_days = None
        if expires_input:
            try:
                expires_days = int(expires_input)
            except ValueError:
                print("⚠️  无效的天数，将设置为永不过期")
        
        rate_limit_input = input("请输入速率限制(每分钟请求数，默认: 1000): ").strip()
        rate_limit = 1000
        if rate_limit_input:
            try:
                rate_limit = int(rate_limit_input)
            except ValueError:
                print("⚠️  无效的速率限制，将使用默认值1000")
        
        # 创建API key
        request = CreateAPIKeyRequest(
            name=name,
            expires_days=expires_days,
            rate_limit=rate_limit,
            metadata={"type": "admin", "created_by": "init_script"}
        )
        
        print("\n🔄 创建API key...")
        api_key = await auth_manager.create_api_key(request)
        
        print("\n✅ API Key创建成功!")
        print("=" * 50)
        print(f"名称: {api_key.name}")
        print(f"密钥: {api_key.key}")
        print(f"状态: {api_key.status.value}")
        print(f"创建时间: {api_key.created_at}")
        if api_key.expires_at:
            print(f"过期时间: {api_key.expires_at}")
        print(f"速率限制: {api_key.rate_limit}/分钟")
        print("=" * 50)
        
        print("\n⚠️  重要提醒:")
        print("1. 请妥善保存上述API key，它不会再次显示")
        print("2. 将API key添加到客户端请求头中:")
        print(f"   Authorization: Bearer {api_key.key}")
        print("3. 或使用X-API-Key头:")
        print(f"   X-API-Key: {api_key.key}")
        
        # 保存到文件（可选）
        save_choice = input("\n是否将API key保存到文件? (y/N): ").lower()
        if save_choice == 'y':
            key_file = Path("admin_api_key.txt")
            with open(key_file, 'w') as f:
                f.write(f"# MineruAPI 管理员API Key\n")
                f.write(f"# 创建时间: {api_key.created_at}\n")
                f.write(f"# 名称: {api_key.name}\n")
                f.write(f"API_KEY={api_key.key}\n")
            
            print(f"✅ API key已保存到: {key_file.absolute()}")
            print("⚠️  请确保文件安全，不要提交到版本控制系统")
        
        print("\n🎉 认证系统初始化完成!")
        
        # 显示测试命令
        print("\n📋 测试命令:")
        print(f"curl -H 'Authorization: Bearer {api_key.key}' http://localhost:8000/auth/statistics")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    try:
        asyncio.run(init_auth())
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
