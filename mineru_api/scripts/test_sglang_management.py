#!/usr/bin/env python3
"""
测试 sglang 管理功能的脚本
"""
import asyncio
import httpx
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


class SglangTester:
    """sglang 管理功能测试器"""
    
    def __init__(self, base_url="http://localhost:8000", api_key=None):
        self.base_url = base_url
        self.headers = {}
        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"
    
    async def test_status_apis(self):
        """测试状态查询 API"""
        print("🔍 测试状态查询 API...")
        
        apis = [
            ("/sglang/status", "基本状态"),
            ("/sglang/health", "健康状态"),
            ("/sglang/processes", "进程信息"),
            ("/sglang/recommendations", "优化建议")
        ]
        
        results = {}
        
        async with httpx.AsyncClient(timeout=30) as client:
            for endpoint, name in apis:
                try:
                    response = await client.get(
                        f"{self.base_url}{endpoint}",
                        headers=self.headers
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        results[name] = {"success": True, "data": data}
                        print(f"✅ {name}: 成功")
                    else:
                        results[name] = {"success": False, "error": f"HTTP {response.status_code}"}
                        print(f"❌ {name}: HTTP {response.status_code}")
                
                except Exception as e:
                    results[name] = {"success": False, "error": str(e)}
                    print(f"❌ {name}: {e}")
        
        return results
    
    async def test_management_apis(self):
        """测试管理 API（谨慎使用）"""
        print("\n🔧 测试管理 API...")
        print("⚠️  这将影响 sglang 服务，请确认是否继续？")
        
        # 在实际测试中，你可能不想自动执行这些操作
        # 这里只是展示如何调用
        
        management_apis = [
            ("/sglang/start", "POST", "启动服务"),
            ("/sglang/restart", "POST", "重启服务"),
            # ("/sglang/force-restart", "POST", "强制重启"),  # 谨慎使用
            # ("/sglang/kill-all-processes", "POST", "杀死所有进程"),  # 谨慎使用
        ]
        
        print("跳过管理 API 测试（避免影响运行中的服务）")
        print("如需测试，请手动调用相应接口")
        
        return {"skipped": True, "reason": "避免影响运行中的服务"}
    
    async def test_direct_functions(self):
        """直接测试功能模块"""
        print("\n🧪 测试功能模块...")
        
        try:
            from mineru_api.services.sglang_health_monitor import sglang_health_monitor
            from mineru_api.services.sglang_manager import sglang_manager
            
            # 测试健康检查
            print("测试健康检查...")
            health_result = await sglang_health_monitor.health_check()
            print(f"健康状态: {'✅ 健康' if health_result['healthy'] else '❌ 不健康'}")
            
            if not health_result['healthy']:
                print(f"错误信息: {health_result.get('error', '未知')}")
            
            # 测试状态获取
            print("测试状态获取...")
            status = await sglang_manager.get_status()
            print(f"服务运行: {'✅ 是' if status.get('running') else '❌ 否'}")
            print(f"健康检查: {'✅ 通过' if status.get('health_check') else '❌ 失败'}")
            
            # 测试进程信息
            print("测试进程信息...")
            detailed_status = await sglang_health_monitor.get_detailed_status()
            process_info = detailed_status.get("health_check", {}).get("process_info", {})
            total_processes = process_info.get("total_processes", 0)
            memory_usage = process_info.get("memory_usage", 0)
            
            print(f"进程数量: {total_processes}")
            print(f"内存使用: {memory_usage:.1f} MB")
            
            return {
                "health_check": health_result,
                "status": status,
                "process_info": process_info
            }
        
        except Exception as e:
            print(f"❌ 功能模块测试失败: {e}")
            return {"error": str(e)}
    
    async def show_current_status(self):
        """显示当前状态概览"""
        print("\n📊 当前状态概览")
        print("=" * 50)
        
        try:
            # 获取详细状态
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(
                    f"{self.base_url}/sglang/health",
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 健康状态
                    health = data.get("health_check", {})
                    healthy = health.get("healthy", False)
                    response_time = health.get("response_time")
                    
                    print(f"服务状态: {'🟢 健康' if healthy else '🔴 异常'}")
                    if response_time:
                        print(f"响应时间: {response_time:.2f}s")
                    
                    # 进程信息
                    process_info = health.get("process_info", {})
                    total_processes = process_info.get("total_processes", 0)
                    memory_usage = process_info.get("memory_usage", 0)
                    cpu_usage = process_info.get("cpu_usage", 0)
                    
                    print(f"进程数量: {total_processes}")
                    print(f"内存使用: {memory_usage:.1f} MB")
                    print(f"CPU使用: {cpu_usage:.1f}%")
                    
                    # 监控信息
                    monitor_info = data.get("monitor_info", {})
                    consecutive_failures = monitor_info.get("consecutive_failures", 0)
                    last_restart = monitor_info.get("last_restart_time")
                    
                    print(f"连续失败: {consecutive_failures} 次")
                    print(f"上次重启: {last_restart or '无'}")
                    
                    # 建议
                    recommendations = data.get("recommendations", [])
                    if recommendations:
                        print("\n💡 优化建议:")
                        for rec in recommendations:
                            print(f"  - {rec}")
                    else:
                        print("\n✅ 无需优化")
                
                else:
                    print(f"❌ 获取状态失败: HTTP {response.status_code}")
        
        except Exception as e:
            print(f"❌ 获取状态失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 sglang 管理功能测试")
    print("=" * 50)
    
    # 创建测试器
    tester = SglangTester()
    
    # 显示当前状态
    await tester.show_current_status()
    
    # 测试状态查询 API
    status_results = await tester.test_status_apis()
    
    # 测试功能模块
    function_results = await tester.test_direct_functions()
    
    # 测试管理 API（跳过）
    management_results = await tester.test_management_apis()
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)
    
    # 状态 API 结果
    print("\n🔍 状态查询 API:")
    for name, result in status_results.items():
        status = "✅ 成功" if result["success"] else f"❌ 失败: {result['error']}"
        print(f"  {name}: {status}")
    
    # 功能模块结果
    print("\n🧪 功能模块:")
    if "error" in function_results:
        print(f"  ❌ 测试失败: {function_results['error']}")
    else:
        print("  ✅ 基本功能正常")
    
    # 给出建议
    print("\n💡 使用建议:")
    print("  1. 如果服务状态异常，可以尝试:")
    print("     curl -X POST http://localhost:8000/sglang/force-restart")
    print("  2. 定期检查服务状态:")
    print("     curl http://localhost:8000/sglang/health")
    print("  3. 查看进程信息:")
    print("     curl http://localhost:8000/sglang/processes")


if __name__ == "__main__":
    asyncio.run(main())
