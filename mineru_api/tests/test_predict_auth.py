#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
测试 /predict 接口的认证保护
"""

import asyncio
import httpx
import base64
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


async def test_predict_auth():
    """测试 /predict 接口的认证保护"""
    print("🧪 测试 /predict 接口认证保护")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 准备测试数据
    test_data = {
        "file_name": "test.pdf",
        "file_content": base64.b64encode(b"fake pdf content").decode(),
        "lang": "ch",
        "backend": "pipeline"
    }
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            
            # 1. 测试无认证访问 /predict
            print("1️⃣ 测试无认证访问 /predict...")
            try:
                response = await client.post(f"{base_url}/predict", json=test_data)
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 401:
                    print("   ✅ /predict 接口已受认证保护")
                    print(f"   响应: {response.json()}")
                elif response.status_code == 200:
                    print("   ❌ /predict 接口未受保护！存在安全漏洞")
                    return False
                else:
                    print(f"   ⚠️  意外的状态码: {response.status_code}")
                    print(f"   响应: {response.text}")
                    
            except Exception as e:
                print(f"   ❌ 请求失败: {e}")
                return False
            
            # 2. 测试无认证访问 /parse/upload
            print("\n2️⃣ 测试无认证访问 /parse/upload...")
            try:
                # 创建表单数据
                files = {"file": ("test.pdf", b"fake pdf content", "application/pdf")}
                data = {"lang": "ch", "backend": "pipeline"}
                
                response = await client.post(f"{base_url}/parse/upload", files=files, data=data)
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 401:
                    print("   ✅ /parse/upload 接口已受认证保护")
                    print(f"   响应: {response.json()}")
                else:
                    print(f"   ⚠️  意外的状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ 请求失败: {e}")
            
            # 3. 测试公开接口（不需要认证）
            print("\n3️⃣ 测试公开接口...")
            public_endpoints = ["/health", "/docs", "/openapi.json"]
            
            for endpoint in public_endpoints:
                try:
                    response = await client.get(f"{base_url}{endpoint}")
                    print(f"   {endpoint}: {response.status_code}")
                    
                    if response.status_code in [200, 404]:  # 404也是正常的，说明没有被认证拦截
                        print(f"     ✅ 公开接口正常")
                    else:
                        print(f"     ⚠️  意外状态码: {response.status_code}")
                        
                except Exception as e:
                    print(f"     ❌ 请求失败: {e}")
            
            print("\n" + "=" * 50)
            print("🎉 认证保护测试完成")
            return True
            
    except httpx.ConnectError:
        print("❌ 无法连接到服务器")
        print("请确保服务器正在运行: python mineru_api/start_server.py")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_with_valid_api_key():
    """使用有效API key测试"""
    print("\n🔑 测试有效API key访问")
    print("=" * 50)
    
    # 注意：这里需要一个有效的API key
    # 可以通过 python scripts/init_auth.py 创建
    api_key = input("请输入有效的API key (或按Enter跳过): ").strip()
    
    if not api_key:
        print("跳过API key测试")
        return True
    
    base_url = "http://localhost:8000"
    headers = {"Authorization": f"Bearer {api_key}"}
    
    # 准备测试数据
    test_data = {
        "file_name": "test.pdf", 
        "file_content": base64.b64encode(b"fake pdf content").decode(),
        "lang": "ch",
        "backend": "pipeline"
    }
    
    try:
        async with httpx.AsyncClient(timeout=10.0, headers=headers) as client:
            
            # 测试 /predict 接口
            print("1️⃣ 测试带认证的 /predict...")
            response = await client.post(f"{base_url}/predict", json=test_data)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 认证成功，接口正常工作")
                result = response.json()
                print(f"   任务ID: {result.get('task_id', 'N/A')}")
            elif response.status_code == 401:
                print("   ❌ API key无效或已过期")
                return False
            else:
                print(f"   ⚠️  意外状态码: {response.status_code}")
                print(f"   响应: {response.text}")
            
            # 测试认证统计接口
            print("\n2️⃣ 测试认证统计接口...")
            response = await client.get(f"{base_url}/auth/statistics")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 认证统计接口正常")
                stats = response.json()
                print(f"   统计: {stats}")
            else:
                print(f"   ⚠️  状态码: {response.status_code}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🔐 MineruAPI 认证保护测试")
    print("=" * 60)
    
    # 测试认证保护
    success1 = await test_predict_auth()
    
    # 测试有效API key
    success2 = await test_with_valid_api_key()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("✅ /predict 接口已受到认证保护")
        print("✅ 认证系统工作正常")
    else:
        print("\n❌ 部分测试失败")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试已取消")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        sys.exit(1)
