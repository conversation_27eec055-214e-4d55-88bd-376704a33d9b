"""
回调可靠性测试脚本
测试新增的回调跟踪和重试功能
"""
import asyncio
import json
import time
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from mineru_api.services.callback_tracker import callback_tracker
from mineru_api.services.task_manager import task_manager
from mineru_api.models import OCRRequest, TaskStatus
from mineru_api.utils.callback_enhancer import init_callback_enhancements, cleanup_callback_enhancements


def test_callback_tracking():
    """测试回调跟踪功能"""
    print("🧪 测试回调跟踪功能...")
    
    # 模拟任务
    task_id = "test-task-001"
    callback_url = "http://localhost:8010/api/v1/document/callback"
    
    # 开始跟踪回调
    callback_tracker.track_callback(task_id, callback_url)
    
    # 检查跟踪状态
    status = callback_tracker.get_callback_status(task_id)
    assert status is not None
    assert status["status"] == "pending"
    print(f"✅ 回调跟踪已开始: {status}")
    
    # 模拟回调失败
    callback_tracker.mark_callback_failed(task_id, "Connection refused")
    status = callback_tracker.get_callback_status(task_id)
    assert status["status"] == "failed"
    assert status["attempts"] == 1
    print(f"✅ 回调失败已记录: {status}")
    
    # 模拟回调成功
    callback_tracker.mark_callback_success(task_id)
    status = callback_tracker.get_callback_status(task_id)
    assert status["status"] == "success"
    print(f"✅ 回调成功已记录: {status}")


def test_retry_mechanism():
    """测试重试机制"""
    print("\n🧪 测试重试机制...")
    
    # 启动重试工作线程
    callback_tracker.start_retry_worker()
    
    # 模拟失败的回调
    task_id = "test-task-002"
    callback_url = "http://invalid-url/callback"
    
    callback_tracker.track_callback(task_id, callback_url)
    callback_tracker.mark_callback_failed(task_id, "Network error")
    
    # 检查待重试的回调
    pending = callback_tracker.get_pending_callbacks()
    print(f"✅ 待重试回调数量: {len(pending)}")
    
    # 等待一段时间让重试机制工作
    print("⏳ 等待重试机制工作...")
    time.sleep(5)
    
    # 停止重试工作线程
    callback_tracker.stop_retry_worker()
    print("✅ 重试机制测试完成")


def test_task_status_api():
    """测试任务状态API（模拟）"""
    print("\n🧪 测试任务状态API...")
    
    # 创建模拟任务
    request = OCRRequest(
        file_name="test.pdf",
        file_content=b"test content",
        callback_url="http://localhost:8010/callback"
    )
    
    task_id = "test-task-003"
    task = task_manager.create_task(task_id, request)
    
    # 检查任务状态
    status = task_manager.get_task_status(task_id)
    assert status is not None
    assert status.status == TaskStatus.PENDING
    print(f"✅ 任务状态查询: {status.status}")
    
    # 更新任务状态
    task_manager.update_task_status(task_id, TaskStatus.PROCESSING, "正在处理")
    status = task_manager.get_task_status(task_id)
    assert status.status == TaskStatus.PROCESSING
    print(f"✅ 任务状态更新: {status.status}")


def test_data_persistence():
    """测试数据持久化"""
    print("\n🧪 测试数据持久化...")
    
    # 创建测试数据
    task_id = "test-task-004"
    callback_url = "http://localhost:8010/callback"
    
    callback_tracker.track_callback(task_id, callback_url)
    callback_tracker.mark_callback_failed(task_id, "Test error")
    
    # 检查数据文件是否存在
    storage_file = callback_tracker._storage_file
    assert storage_file.exists()
    print(f"✅ 数据文件已创建: {storage_file}")
    
    # 读取数据文件
    with open(storage_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    assert task_id in data
    assert data[task_id]["status"] == "failed"
    print(f"✅ 数据持久化正常: {len(data)} 条记录")


def test_cleanup():
    """测试清理功能"""
    print("\n🧪 测试清理功能...")
    
    # 创建一些测试数据
    for i in range(5):
        task_id = f"test-task-cleanup-{i}"
        callback_tracker.track_callback(task_id, "http://test.com/callback")
        if i % 2 == 0:
            callback_tracker.mark_callback_success(task_id)
        else:
            callback_tracker.mark_callback_failed(task_id, "Test error")
    
    initial_count = len(callback_tracker._records)
    print(f"清理前记录数: {initial_count}")
    
    # 执行清理（设置很短的保留时间）
    callback_tracker.cleanup_old_records(max_age_hours=0)
    
    final_count = len(callback_tracker._records)
    print(f"清理后记录数: {final_count}")
    print("✅ 清理功能测试完成")


def main():
    """主测试函数"""
    print("🚀 开始回调可靠性测试...")
    
    try:
        # 初始化回调增强功能
        init_callback_enhancements()
        
        # 运行测试
        test_callback_tracking()
        test_retry_mechanism()
        test_task_status_api()
        test_data_persistence()
        test_cleanup()
        
        print("\n🎉 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理资源
        cleanup_callback_enhancements()
        print("\n🧹 测试资源已清理")


if __name__ == "__main__":
    main()
