"""
多格式文件测试脚本
"""
import asyncio
import base64
from pathlib import Path
from test_client import MineruAPIClient
from loguru import logger


async def test_multi_format_files():
    """测试多种格式文件的解析"""
    client = MineruAPIClient("http://localhost:8000")
    
    # 定义测试文件类型和路径
    test_cases = [
        {
            "type": "PDF",
            "extensions": [".pdf"],
            "test_dir": Path(__file__).parent.parent / "demo" / "pdfs"
        },
        {
            "type": "Word文档",
            "extensions": [".doc", ".docx"],
            "test_dir": Path(__file__).parent / "test_files"
        },
        {
            "type": "PowerPoint",
            "extensions": [".ppt", ".pptx"],
            "test_dir": Path(__file__).parent / "test_files"
        },
        {
            "type": "Excel",
            "extensions": [".xls", ".xlsx"],
            "test_dir": Path(__file__).parent / "test_files"
        },
        {
            "type": "图像",
            "extensions": [".jpg", ".jpeg", ".png"],
            "test_dir": Path(__file__).parent / "test_files"
        }
    ]
    
    try:
        # 健康检查
        health = await client.health_check()
        logger.info(f"服务状态: {health}")
        
        successful_tests = 0
        total_tests = 0
        
        for test_case in test_cases:
            logger.info(f"\n=== 测试 {test_case['type']} 文件 ===")
            
            # 查找测试文件
            test_files = []
            if test_case["test_dir"].exists():
                for ext in test_case["extensions"]:
                    test_files.extend(list(test_case["test_dir"].glob(f"*{ext}")))
            
            if not test_files:
                logger.warning(f"未找到 {test_case['type']} 测试文件")
                continue
            
            # 测试第一个找到的文件
            test_file = test_files[0]
            logger.info(f"测试文件: {test_file.name}")
            
            try:
                total_tests += 1
                
                # 提交解析任务
                result = await client.parse_file_upload(
                    test_file,
                    lang="ch",
                    backend="pipeline",
                    method="auto"
                )
                
                task_id = result["task_id"]
                logger.info(f"任务已提交: {task_id}")
                
                # 等待任务完成
                final_status = await client.wait_for_completion(task_id, timeout=300)
                
                if final_status and final_status["status"] == "completed":
                    logger.success(f"✅ {test_case['type']} 文件解析成功!")
                    
                    result_data = final_status.get("result", {})
                    files = result_data.get("files", {})
                    metadata = result_data.get("metadata", {})
                    
                    logger.info(f"生成文件: {list(files.keys())}")
                    logger.info(f"转换类型: {metadata.get('converted_from', 'N/A')}")
                    
                    successful_tests += 1
                else:
                    logger.error(f"❌ {test_case['type']} 文件解析失败")
                    if final_status:
                        logger.error(f"错误信息: {final_status.get('error', '未知错误')}")
                
            except Exception as e:
                logger.error(f"❌ {test_case['type']} 文件测试异常: {e}")
        
        # 测试结果汇总
        logger.info(f"\n=== 测试结果汇总 ===")
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"成功数: {successful_tests}")
        logger.info(f"失败数: {total_tests - successful_tests}")
        logger.info(f"成功率: {successful_tests / total_tests * 100:.1f}%" if total_tests > 0 else "无测试")
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
    finally:
        await client.close()


async def test_unsupported_format():
    """测试不支持的文件格式"""
    logger.info("\n=== 测试不支持的文件格式 ===")
    
    client = MineruAPIClient("http://localhost:8000")
    
    try:
        # 创建一个不支持的文件格式测试
        test_content = b"This is a test file content"
        
        # 使用 JSON 请求方式测试
        request_data = {
            "file_name": "test.txt",  # 不支持的格式
            "file_content": base64.b64encode(test_content).decode(),
            "lang": "ch",
            "backend": "pipeline"
        }
        
        response = await client.client.post(
            f"{client.base_url}/predict",
            json=request_data
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result["task_id"]
            logger.info(f"不支持格式任务已提交: {task_id}")
            
            # 等待任务完成（应该失败）
            final_status = await client.wait_for_completion(task_id, timeout=60)
            
            if final_status and final_status["status"] == "failed":
                logger.success("✅ 不支持格式正确被拒绝")
                logger.info(f"错误信息: {final_status.get('error', '未知错误')}")
            else:
                logger.error("❌ 不支持格式未被正确拒绝")
        else:
            logger.info(f"请求被拒绝，状态码: {response.status_code}")
            
    except Exception as e:
        logger.error(f"不支持格式测试异常: {e}")
    finally:
        await client.close()


def create_test_files():
    """创建测试文件目录和示例文件"""
    test_dir = Path(__file__).parent / "test_files"
    test_dir.mkdir(exist_ok=True)
    
    logger.info(f"测试文件目录: {test_dir}")
    logger.info("请在此目录中放置以下格式的测试文件:")
    logger.info("- Word文档: .doc, .docx")
    logger.info("- PowerPoint: .ppt, .pptx") 
    logger.info("- Excel: .xls, .xlsx")
    logger.info("- 图像: .jpg, .jpeg, .png")
    
    return test_dir


async def main():
    """主函数"""
    logger.info("MineruAPI 多格式文件测试")
    logger.info("请确保 MineruAPI 服务已启动")
    
    # 创建测试文件目录
    test_dir = create_test_files()
    
    # 检查是否有测试文件
    has_test_files = any(test_dir.glob("*.*"))
    
    if not has_test_files:
        logger.warning(f"测试目录 {test_dir} 中没有测试文件")
        logger.info("将只测试 demo/pdfs 目录中的 PDF 文件")
    
    input("按 Enter 键开始多格式文件测试...")
    await test_multi_format_files()
    
    input("\n按 Enter 键测试不支持的文件格式...")
    await test_unsupported_format()
    
    logger.info("所有测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
