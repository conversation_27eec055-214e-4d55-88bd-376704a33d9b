#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
认证功能测试
"""

import asyncio
import httpx
import pytest
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from mineru_api.config import get_auth_config
from mineru_api.auth.manager import AuthManager
from mineru_api.auth.models import CreateAPIKeyRequest


class TestAuth:
    """认证功能测试"""
    
    @pytest.fixture
    async def auth_manager(self):
        """创建认证管理器"""
        config = get_auth_config()
        # 使用测试配置
        config.file_path = "test_api_keys.json"
        config.sqlite_path = "test_auth.db"
        
        manager = AuthManager(config)
        yield manager
        
        # 清理测试文件
        test_files = [
            Path("test_api_keys.json"),
            Path("test_auth.db")
        ]
        for file in test_files:
            if file.exists():
                file.unlink()
    
    @pytest.mark.asyncio
    async def test_create_api_key(self, auth_manager):
        """测试创建API key"""
        request = CreateAPIKeyRequest(
            name="测试应用",
            expires_days=30,
            rate_limit=100
        )
        
        api_key = await auth_manager.create_api_key(request)
        
        assert api_key.name == "测试应用"
        assert api_key.key.startswith("mk_")
        assert api_key.rate_limit == 100
        assert api_key.expires_at is not None
    
    @pytest.mark.asyncio
    async def test_verify_api_key(self, auth_manager):
        """测试验证API key"""
        # 创建API key
        request = CreateAPIKeyRequest(name="测试验证")
        api_key = await auth_manager.create_api_key(request)
        
        # 验证有效的API key
        verified_key = await auth_manager.verify_api_key(api_key.key)
        assert verified_key is not None
        assert verified_key.name == "测试验证"
        
        # 验证无效的API key
        invalid_key = await auth_manager.verify_api_key("invalid_key")
        assert invalid_key is None
    
    @pytest.mark.asyncio
    async def test_revoke_api_key(self, auth_manager):
        """测试撤销API key"""
        # 创建API key
        request = CreateAPIKeyRequest(name="测试撤销")
        api_key = await auth_manager.create_api_key(request)
        
        # 撤销API key
        success = await auth_manager.revoke_api_key(api_key.key)
        assert success is True
        
        # 验证已撤销的API key
        verified_key = await auth_manager.verify_api_key(api_key.key)
        assert verified_key is None
    
    @pytest.mark.asyncio
    async def test_list_api_keys(self, auth_manager):
        """测试列出API keys"""
        # 创建多个API key
        for i in range(3):
            request = CreateAPIKeyRequest(name=f"测试应用{i}")
            await auth_manager.create_api_key(request)
        
        # 列出API keys
        api_keys = await auth_manager.list_api_keys()
        assert len(api_keys) == 3
        
        # 撤销一个
        first_key = api_keys[0]
        # 需要获取完整的API key来撤销
        all_keys = await auth_manager.backend.list_api_keys()
        await auth_manager.revoke_api_key(all_keys[0].key)
        
        # 列出活跃的API keys
        active_keys = await auth_manager.list_api_keys(include_revoked=False)
        assert len(active_keys) == 2
        
        # 列出所有API keys
        all_keys = await auth_manager.list_api_keys(include_revoked=True)
        assert len(all_keys) == 3


async def test_auth_integration():
    """集成测试"""
    print("🧪 开始认证功能集成测试")
    
    # 创建认证管理器
    config = get_auth_config()
    config.file_path = "integration_test_keys.json"
    auth_manager = AuthManager(config)
    
    try:
        # 1. 创建API key
        print("1️⃣ 创建API key...")
        request = CreateAPIKeyRequest(
            name="集成测试",
            rate_limit=50
        )
        api_key = await auth_manager.create_api_key(request)
        print(f"   ✅ 创建成功: {api_key.key[:10]}...")
        
        # 2. 验证API key
        print("2️⃣ 验证API key...")
        verified = await auth_manager.verify_api_key(api_key.key)
        assert verified is not None
        print("   ✅ 验证成功")
        
        # 3. 更新使用统计
        print("3️⃣ 更新使用统计...")
        await auth_manager.update_usage(api_key.key)
        
        # 获取更新后的信息
        updated_key = await auth_manager.get_api_key_info(api_key.key)
        assert updated_key.usage_count == 1
        print("   ✅ 统计更新成功")
        
        # 4. 获取统计信息
        print("4️⃣ 获取统计信息...")
        stats = await auth_manager.get_statistics()
        assert stats['total'] >= 1
        assert stats['active'] >= 1
        print(f"   ✅ 统计信息: {stats}")
        
        # 5. 撤销API key
        print("5️⃣ 撤销API key...")
        success = await auth_manager.revoke_api_key(api_key.key)
        assert success is True
        print("   ✅ 撤销成功")
        
        # 6. 验证撤销后的API key
        print("6️⃣ 验证撤销后的API key...")
        revoked_key = await auth_manager.verify_api_key(api_key.key)
        assert revoked_key is None
        print("   ✅ 撤销验证成功")
        
        print("🎉 所有测试通过!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        raise
    finally:
        # 清理测试文件
        test_file = Path("integration_test_keys.json")
        if test_file.exists():
            test_file.unlink()


async def test_http_client():
    """测试HTTP客户端认证"""
    print("🌐 测试HTTP客户端认证")
    
    # 注意：这需要服务器运行
    base_url = "http://localhost:8000"
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 测试无认证访问
            response = await client.get(f"{base_url}/auth/statistics")
            print(f"无认证访问状态码: {response.status_code}")
            
            # 如果启用了认证，应该返回401
            if response.status_code == 401:
                print("   ✅ 认证保护正常工作")
            else:
                print("   ⚠️  认证可能未启用或服务器未运行")
                
    except httpx.ConnectError:
        print("   ⚠️  无法连接到服务器，跳过HTTP测试")
    except Exception as e:
        print(f"   ❌ HTTP测试失败: {e}")


def main():
    """主函数"""
    print("🔐 MineruAPI 认证功能测试")
    print("=" * 50)
    
    try:
        # 运行集成测试
        asyncio.run(test_auth_integration())
        
        print("\n" + "=" * 50)
        
        # 运行HTTP测试
        asyncio.run(test_http_client())
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
