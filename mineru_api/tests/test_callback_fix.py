"""
测试回调修复效果
"""
import asyncio
import time
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from loguru import logger
from mineru_api.utils.callback_utils import callback_handler
from mineru_api.services.callback_service import callback_service


async def test_callback_service():
    """测试回调服务的连接管理"""
    logger.info("开始测试回调服务...")
    
    # 模拟多次回调调用
    for i in range(5):
        try:
            logger.info(f"测试回调 {i+1}/5")
            
            # 模拟任务数据
            from mineru_api.services.task_manager import task_manager
            from mineru_api.models import TaskStatus, TaskStatusResponse, TaskResult
            from datetime import datetime
            
            task_id = f"test-task-{i+1}"
            
            # 创建模拟任务状态
            task_status = TaskStatusResponse(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                message="测试完成",
                created_at=datetime.now()
            )
            
            # 创建模拟结果
            task_result = TaskResult(
                task_id=task_id,
                file_name=f"test-{i+1}.pdf",
                output_dir="/tmp/test",
                files={"markdown": f"test-{i+1}.md"},
                metadata={"test": True}
            )
            
            # 设置任务状态和结果
            with task_manager._lock:
                task_manager.tasks[task_id] = task_status
                task_manager.results[task_id] = task_result
            
            # 发送回调（使用无效URL测试错误处理）
            await callback_service.send_callback(
                task_id=task_id,
                callback_url="http://invalid-url-for-testing.local/webhook",
                headers={"Test": "true"}
            )
            
            logger.info(f"回调 {i+1} 测试完成")
            
        except Exception as e:
            logger.error(f"回调 {i+1} 测试失败: {e}")
        
        # 短暂等待
        await asyncio.sleep(1)
    
    logger.info("回调服务测试完成")


def test_callback_handler():
    """测试回调处理器的线程安全性"""
    logger.info("开始测试回调处理器...")
    
    # 模拟多个并发回调
    for i in range(3):
        try:
            task_id = f"handler-test-{i+1}"
            logger.info(f"提交回调处理器测试 {i+1}/3")
            
            callback_handler.send_callback_sync(
                task_id=task_id,
                callback_url="http://invalid-url-for-testing.local/webhook",
                headers={"Handler-Test": "true"}
            )
            
        except Exception as e:
            logger.error(f"回调处理器测试 {i+1} 失败: {e}")
    
    # 等待回调处理完成
    logger.info("等待回调处理完成...")
    time.sleep(10)
    
    logger.info("回调处理器测试完成")


async def main():
    """主测试函数"""
    logger.info("=== 开始回调修复测试 ===")
    
    try:
        # 测试回调服务
        await test_callback_service()
        
        # 测试回调处理器
        test_callback_handler()
        
        logger.info("=== 所有测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
    
    finally:
        # 清理资源
        logger.info("清理测试资源...")
        try:
            callback_handler.shutdown()
            await callback_service.close()
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
