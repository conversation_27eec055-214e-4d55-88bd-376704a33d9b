# -*- encoding: utf-8 -*-
"""
@File   :test_upload.py
@Time   :2025/7/1 15:37
<AUTHOR>
"""
import base64
import os

import requests


def to_b64(file_path):
    try:
        with open(file_path, 'rb') as f:
            return base64.b64encode(f.read()).decode('utf-8')
    except Exception as e:
        raise Exception(f'File: {file_path} - Info: {e}')


def do_parse(file_path, url='http://127.0.0.1:2233/predict', **kwargs):
    try:
        response = requests.post(url, json={
            'file_content': to_b64(file_path),
            'file_name': os.path.basename(file_path),
            **kwargs
        })

        if response.status_code == 200:
            output = response.json()
            output['file_path'] = file_path
            return output
        else:
            raise Exception(response.text)
    except Exception as e:
        print(f'File: {file_path} - Info: {e}')


path = r'D:\WXWork\1688857100340944\Cache\File\2025-07\运单.pdf'
do_parse(path, )
