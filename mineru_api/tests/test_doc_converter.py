"""
文档转换器测试脚本
"""
import sys
from pathlib import Path
from loguru import logger

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from services.doc_converter import DocConverter, ConvertError
from config import TEMP_DIR


def test_file_type_detection():
    """测试文件类型检测"""
    logger.info("=== 测试文件类型检测 ===")
    
    test_cases = [
        ("document.pdf", "pdf"),
        ("presentation.ppt", "office"),
        ("spreadsheet.xlsx", "office"),
        ("image.jpg", "image"),
        ("photo.png", "image"),
        ("unknown.txt", "unknown")
    ]
    
    for file_name, expected_type in test_cases:
        file_path = Path(file_name)
        detected_type = DocConverter.get_file_type(file_path)
        status = "✅" if detected_type == expected_type else "❌"
        logger.info(f"{status} {file_name} -> {detected_type} (期望: {expected_type})")


def test_format_support():
    """测试格式支持检测"""
    logger.info("\n=== 测试格式支持检测 ===")
    
    test_cases = [
        ("document.pdf", True),
        ("document.doc", True),
        ("document.docx", True),
        ("presentation.ppt", True),
        ("presentation.pptx", True),
        ("spreadsheet.xls", True),
        ("spreadsheet.xlsx", True),
        ("image.jpg", True),
        ("image.jpeg", True),
        ("image.png", True),
        ("image.bmp", True),
        ("image.tiff", True),
        ("video.mp4", False),
        ("archive.zip", False),
        ("text.txt", False)
    ]
    
    for file_name, expected_support in test_cases:
        file_path = Path(file_name)
        is_supported = DocConverter.is_supported_format(file_path)
        status = "✅" if is_supported == expected_support else "❌"
        logger.info(f"{status} {file_name} -> {'支持' if is_supported else '不支持'}")


def test_image_conversion():
    """测试图像转换功能"""
    logger.info("\n=== 测试图像转换功能 ===")
    
    # 创建一个简单的测试图像
    try:
        from PIL import Image
        
        # 创建测试图像
        test_image = Image.new('RGB', (100, 100), color='red')
        test_image_path = TEMP_DIR / "test_image.png"
        test_image.save(test_image_path)
        
        logger.info(f"创建测试图像: {test_image_path}")
        
        # 测试转换
        output_dir = TEMP_DIR / "test_conversion"
        output_dir.mkdir(exist_ok=True)
        
        try:
            pdf_path = DocConverter.convert_to_pdf(test_image_path, output_dir, "test_task")
            
            if pdf_path.exists():
                logger.success(f"✅ 图像转换成功: {pdf_path}")
                logger.info(f"PDF文件大小: {pdf_path.stat().st_size} bytes")
                
                # 清理测试文件
                pdf_path.unlink()
            else:
                logger.error("❌ PDF文件未生成")
                
        except ConvertError as e:
            logger.error(f"❌ 图像转换失败: {e}")
        
        # 清理测试图像
        test_image_path.unlink()
        
    except ImportError:
        logger.warning("PIL库未安装，跳过图像转换测试")
    except Exception as e:
        logger.error(f"图像转换测试异常: {e}")


def test_office_conversion_availability():
    """测试Office转换功能可用性"""
    logger.info("\n=== 测试Office转换功能可用性 ===")
    
    if sys.platform == "win32":
        logger.info("Windows平台 - 检查Office COM组件")
        try:
            import comtypes.client
            
            # 测试Word
            try:
                word = comtypes.client.CreateObject('Word.Application')
                word.Visible = False
                word.Quit()
                logger.success("✅ Microsoft Word 可用")
            except Exception as e:
                logger.warning(f"⚠️ Microsoft Word 不可用: {e}")
            
            # 测试PowerPoint
            try:
                ppt = comtypes.client.CreateObject('PowerPoint.Application')
                ppt.Quit()
                logger.success("✅ Microsoft PowerPoint 可用")
            except Exception as e:
                logger.warning(f"⚠️ Microsoft PowerPoint 不可用: {e}")
            
            # 测试Excel
            try:
                excel = comtypes.client.CreateObject('Excel.Application')
                excel.Visible = False
                excel.Quit()
                logger.success("✅ Microsoft Excel 可用")
            except Exception as e:
                logger.warning(f"⚠️ Microsoft Excel 不可用: {e}")
                
        except ImportError:
            logger.error("❌ comtypes库未安装")
            
    else:
        logger.info("Linux平台 - 检查LibreOffice")
        import subprocess
        
        try:
            result = subprocess.run(
                ['soffice', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                logger.success(f"✅ LibreOffice 可用: {result.stdout.strip()}")
            else:
                logger.error("❌ LibreOffice 不可用")
                
        except FileNotFoundError:
            logger.error("❌ LibreOffice 未安装")
        except subprocess.TimeoutExpired:
            logger.error("❌ LibreOffice 响应超时")
        except Exception as e:
            logger.error(f"❌ LibreOffice 检查失败: {e}")


def test_pdf_copy():
    """测试PDF文件复制功能"""
    logger.info("\n=== 测试PDF文件复制功能 ===")
    
    # 创建一个简单的测试PDF内容
    test_pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
    test_pdf_path = TEMP_DIR / "test.pdf"
    
    with open(test_pdf_path, 'wb') as f:
        f.write(test_pdf_content)
    
    logger.info(f"创建测试PDF: {test_pdf_path}")
    
    try:
        output_dir = TEMP_DIR / "test_pdf_copy"
        output_dir.mkdir(exist_ok=True)
        
        copied_pdf = DocConverter.convert_to_pdf(test_pdf_path, output_dir, "test_task")
        
        if copied_pdf.exists():
            logger.success(f"✅ PDF复制成功: {copied_pdf}")
            
            # 验证内容
            with open(copied_pdf, 'rb') as f:
                copied_content = f.read()
            
            if copied_content == test_pdf_content:
                logger.success("✅ PDF内容验证成功")
            else:
                logger.error("❌ PDF内容验证失败")
            
            # 清理
            copied_pdf.unlink()
        else:
            logger.error("❌ PDF复制失败")
            
    except Exception as e:
        logger.error(f"PDF复制测试异常: {e}")
    finally:
        # 清理测试文件
        if test_pdf_path.exists():
            test_pdf_path.unlink()


def main():
    """主函数"""
    logger.info("文档转换器功能测试")
    logger.info("=" * 50)
    
    # 确保临时目录存在
    TEMP_DIR.mkdir(exist_ok=True)
    
    # 运行所有测试
    test_file_type_detection()
    test_format_support()
    test_pdf_copy()
    test_image_conversion()
    test_office_conversion_availability()
    
    logger.info("\n" + "=" * 50)
    logger.info("所有测试完成!")
    
    # 清理临时目录
    try:
        import shutil
        for item in TEMP_DIR.iterdir():
            if item.is_dir():
                shutil.rmtree(item)
            else:
                item.unlink()
    except Exception as e:
        logger.warning(f"清理临时文件失败: {e}")


if __name__ == "__main__":
    main()
