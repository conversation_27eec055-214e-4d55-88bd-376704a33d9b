# -*- encoding: utf-8 -*-
"""
@File   :test_pipe.py
@Time   :2025/7/1 13:35
<AUTHOR>
"""
import json
import os
import requests
from urllib.parse import urlparse
from base64 import b64encode
from glob import glob
from typing import Tuple, Union, Optional

import uvicorn
from fastapi import FastAPI, UploadFile
from fastapi.responses import JSONResponse
from loguru import logger

from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.data.data_reader_writer.s3 import S3DataReader, S3DataWriter
from mineru.utils.config_reader import get_bucket_name, get_s3_config
from fastapi import Form
from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2
from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json
from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
from mineru.backend.vlm.vlm_analyze import doc_analyze as vlm_doc_analyze
from mineru.backend.vlm.vlm_middle_json_mkcontent import union_make as vlm_union_make
from mineru.utils.enum_class import MakeMode
from mineru.utils.draw_bbox import draw_layout_bbox, draw_span_bbox

pdf_extensions = [".pdf"]


def init_writers(
        file_path: Optional[str] = None,
        file: Optional[UploadFile] = None,
        output_path: Optional[str] = None,
        output_image_path: Optional[str] = None,
) -> Tuple[
    Union[S3DataWriter, FileBasedDataWriter],
    Union[S3DataWriter, FileBasedDataWriter],
    bytes,
    str,
]:
    """
    Initialize writers based on path type

    Args:
        file_path: file path (local path or S3 path)
        file: Uploaded file object
        output_path: Output directory path
        output_image_path: Image output directory path

    Returns:
        Tuple[writer, image_writer, file_bytes, file_extension]: Returns initialized writer tuple and file content
    """
    file_extension: str = ""
    file_bytes: bytes = b""

    output_path_checked = output_path if output_path else "output"
    output_image_path_checked = output_image_path if output_image_path else f"{output_path_checked}/images"

    if file_path:
        is_s3_path = file_path.startswith("s3://")
        if is_s3_path:
            bucket = get_bucket_name(file_path)
            ak, sk, endpoint = get_s3_config(bucket)

            writer = S3DataWriter(
                output_path_checked, bucket=bucket, ak=ak, sk=sk, endpoint_url=endpoint
            )
            image_writer = S3DataWriter(
                output_image_path_checked, bucket=bucket, ak=ak, sk=sk, endpoint_url=endpoint
            )
            # 临时创建reader读取文件内容
            temp_reader = S3DataReader(
                "", bucket=bucket, ak=ak, sk=sk, endpoint_url=endpoint
            )
            file_bytes = temp_reader.read(file_path)
            file_extension = os.path.splitext(file_path)[1]
        else:
            writer = FileBasedDataWriter(output_path_checked)
            image_writer = FileBasedDataWriter(output_image_path_checked)
            os.makedirs(output_image_path_checked, exist_ok=True)
            with open(file_path, "rb") as f:
                file_bytes = f.read()
            file_extension = os.path.splitext(file_path)[1]
    elif file:
        # 处理上传的文件
        content = file.file.read()
        if isinstance(content, str):
            file_bytes = content.encode("utf-8")
        else:
            file_bytes = content
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ""

        writer = FileBasedDataWriter(output_path_checked)
        image_writer = FileBasedDataWriter(output_image_path_checked)
        os.makedirs(output_image_path_checked, exist_ok=True)
    else:
        raise ValueError("Must provide either file or file_path")

    return writer, image_writer, file_bytes, file_extension


def process_file_pipeline(
        file_bytes: bytes,
        file_extension: str,
        image_writer: Union[S3DataWriter, FileBasedDataWriter],
        parse_method: str = "auto",
        lang: str = "ch",
        formula_enable: bool = True,
        table_enable: bool = True,
):
    """Pipeline 模式处理函数"""
    processed_bytes = file_bytes
    if file_extension in pdf_extensions:
        processed_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(file_bytes, 0, None)

    infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
        [processed_bytes], [lang], parse_method, formula_enable, table_enable
    )

    model_list = infer_results[0]
    images_list = all_image_lists[0]
    pdf_doc = all_pdf_docs[0]
    _lang = lang_list[0]
    _ocr_enable = ocr_enabled_list[0]

    model_json = json.loads(json.dumps(model_list))  # deepcopy

    middle_json = pipeline_result_to_middle_json(model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable,
                                                 formula_enable)

    md_content = pipeline_union_make(middle_json["pdf_info"], MakeMode.MM_MD, "images")
    content_list = pipeline_union_make(middle_json["pdf_info"], MakeMode.CONTENT_LIST, "images")

    return model_json, middle_json, content_list, md_content, processed_bytes, middle_json["pdf_info"]


import fastapi

app = FastAPI()


@app.post("/uploadfile/")
async def create_upload_file(file: fastapi.UploadFile = fastapi.File(...)):
    # file_bytes = await file.read()
    # file_extension = os.path.splitext(file.filename)[1]
    writer, image_writer, file_bytes, file_extension = init_writers(
        file_path=None,
        file=file,
        output_path="output",
        output_image_path="output/images",
    )
    model_json, middle_json, content_list, md_content, processed_bytes, pdf_info = process_file_pipeline(
        file_bytes, file_extension, image_writer, "auto", "ch", True, True
    )
    print(model_json)
    print(middle_json)
    print(content_list)
    print(md_content)
    print(processed_bytes)
    print(pdf_info)
    return {"filename": file.filename}


if __name__ == "__main__":
    os.environ["MINERU_MODEL_SOURCE"] = "modelscope"
    uvicorn.run(app, host="0.0.0.0", port=11221)
    # path = r'D:\WXWork\1688857100340944\Cache\File\2025-07\运单.pdf'
    # file_path = path
    # file_name = os.path.basename(file_path)
    # output_dir = "output"
    # file_name = file_name.split(".")[0]
    # output_path = f"{output_dir}/{file_name}"
    # output_image_path = f"{output_path}/images"
    #
    # # Initialize readers/writers and get PDF content
    # writer, image_writer, file_bytes, file_extension = init_writers(
    #     file_path=file_path,
    #     file=None,
    #     output_path=output_path,
    #     output_image_path=output_image_path,
    # )
    # model_json, middle_json, content_list, md_content, processed_bytes, pdf_info = process_file_pipeline(
    #     file_bytes, file_extension, image_writer, "auto", "ch", True, True
    # )
