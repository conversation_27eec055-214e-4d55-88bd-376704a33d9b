# 回调可靠性增强方案

## 🎯 解决的问题

### 核心问题
- **回调失败导致状态不同步**：mineru-api中OCR处理失败时，如果回调也失败，almond_parser永远收不到结果
- **缺乏回调重试机制**：回调失败后只记录日志，没有重试
- **任务超时无通知**：mineru-api任务超时后不会通知almond_parser
- **缺乏主动状态同步**：两个系统间没有状态校验机制

## 🛠️ 解决方案

### 设计原则
1. **最小侵入性**：不修改现有核心代码，通过新增模块实现
2. **向后兼容**：所有新功能都是可选的，不影响现有流程
3. **可观测性**：提供详细的状态查询和监控接口
4. **可恢复性**：支持手动重试和状态修复

### 新增组件

#### 1. 回调状态跟踪器 (`callback_tracker.py`)
- **功能**：跟踪所有回调的状态和重试历史
- **特性**：
  - 持久化回调记录到本地文件
  - 自动重试失败的回调
  - 支持手动重试和状态查询
  - 定期清理过期记录

#### 2. 任务状态查询API (`task_status.py`)
- **功能**：为almond_parser提供主动查询接口
- **接口**：
  - `GET /tasks/{task_id}/status` - 查询任务状态
  - `GET /tasks/{task_id}/result` - 获取任务结果
  - `GET /tasks/{task_id}/callback-status` - 查询回调状态
  - `POST /tasks/{task_id}/retry-callback` - 手动重试回调
  - `GET /tasks/stats` - 获取任务统计
  - `GET /tasks/health` - 健康检查

#### 3. 回调增强器 (`callback_enhancer.py`)
- **功能**：在不修改现有代码的情况下增强回调功能
- **实现**：通过猴子补丁增强现有的回调处理器
- **特性**：
  - 自动跟踪回调执行
  - 记录回调成功/失败状态
  - 启动后台重试工作线程

## 📋 使用方法

### 1. 启动服务
服务启动时会自动初始化回调增强功能：

```bash
cd mineru_api
python main.py
```

### 2. 查询任务状态
almond_parser可以主动查询任务状态：

```python
import httpx

# 查询任务状态
response = httpx.get(
    f"http://mineru-api:8000/tasks/{task_id}/status",
    headers={"Authorization": "Bearer your-api-key"}
)
status = response.json()
```

### 3. 获取任务结果
```python
# 获取任务结果
response = httpx.get(
    f"http://mineru-api:8000/tasks/{task_id}/result",
    headers={"Authorization": "Bearer your-api-key"}
)
result = response.json()
```

### 4. 检查回调状态
```python
# 检查回调状态
response = httpx.get(
    f"http://mineru-api:8000/tasks/{task_id}/callback-status",
    headers={"Authorization": "Bearer your-api-key"}
)
callback_status = response.json()
```

### 5. 手动重试回调
```python
# 手动重试回调
response = httpx.post(
    f"http://mineru-api:8000/tasks/{task_id}/retry-callback",
    headers={"Authorization": "Bearer your-api-key"}
)
```

## 🔧 配置选项

### 环境变量
```bash
# 回调配置（现有）
CALLBACK_TIMEOUT=30          # 回调超时时间（秒）
CALLBACK_RETRY_TIMES=3       # 重试次数
CALLBACK_RETRY_DELAY=5       # 重试间隔（秒）

# 任务配置（现有）
TASK_TIMEOUT=300             # 任务超时时间（秒）
MAX_CONCURRENT_TASKS=5       # 最大并发任务数

# 认证配置（现有）
ENABLE_AUTH=true             # 是否启用认证
```

## 📊 监控和统计

### 健康检查
```bash
curl http://mineru-api:8000/tasks/health
```

### 任务统计
```bash
curl -H "Authorization: Bearer your-api-key" \
     http://mineru-api:8000/tasks/stats
```

## 🧪 测试验证

### 运行测试
```bash
cd mineru_api
python tests/test_callback_reliability.py
```

### 测试内容
- 回调跟踪功能
- 重试机制
- 任务状态API
- 数据持久化
- 清理功能

## 🔄 工作流程

### 正常流程
1. 任务提交 → 开始跟踪回调
2. OCR处理完成 → 发送回调
3. 回调成功 → 标记为成功
4. almond_parser收到结果

### 异常恢复流程
1. 回调失败 → 标记为失败，安排重试
2. 重试工作线程 → 定期重试失败的回调
3. 重试成功 → 标记为成功
4. 重试彻底失败 → 标记为放弃
5. almond_parser主动查询 → 获取任务状态和结果

## 📁 文件结构

```
mineru_api/
├── services/
│   └── callback_tracker.py      # 回调状态跟踪器
├── api/
│   └── task_status.py           # 任务状态查询API
├── utils/
│   └── callback_enhancer.py     # 回调增强器
├── tests/
│   └── test_callback_reliability.py  # 测试脚本
├── data/
│   └── callback_records.json    # 回调记录存储
└── docs/
    └── callback_reliability_enhancement.md  # 本文档
```

## 🚀 部署建议

### 1. 渐进式部署
- 先在测试环境验证功能
- 逐步在生产环境启用
- 监控回调成功率变化

### 2. 监控指标
- 回调成功率
- 重试次数统计
- 任务处理时间
- 系统资源使用

### 3. 告警设置
- 回调失败率超过阈值
- 重试队列积压
- 任务超时频率异常

## 🔮 后续优化

### Phase 2 计划
- almond_parser端的主动监控
- 分布式任务状态同步
- 更智能的重试策略

### Phase 3 计划
- 系统级监控面板
- 自动故障恢复
- 性能优化和扩展
