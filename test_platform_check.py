#!/usr/bin/env python3
"""
测试平台检查功能
"""
import asyncio
import platform
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from mineru_api.tasks.sglang_health_task import sglang_startup_check


async def test_platform_check():
    """测试平台检查功能"""
    print("🧪 测试平台检查功能")
    print("=" * 50)
    print(f"当前平台: {platform.system()}")
    print(f"平台版本: {platform.platform()}")
    print()
    
    print("🚀 执行 sglang_startup_check()...")
    result = await sglang_startup_check()
    
    print("📋 检查结果:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    print()
    if result.get("skipped"):
        print("✅ 平台检查正常工作 - Windows 平台已跳过 sglang 检查")
    else:
        print("⚠️ 平台检查可能有问题 - 应该在 Windows 上跳过")


if __name__ == "__main__":
    asyncio.run(test_platform_check())
