#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
服务状态检查脚本
"""

import asyncio
import aiohttp
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from loguru import logger


async def check_service(url: str, name: str) -> bool:
    """检查服务状态"""
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
            async with session.get(url) as response:
                if response.status == 200:
                    logger.info(f"✅ {name} 服务正常: {url}")
                    return True
                else:
                    logger.warning(f"⚠️  {name} 服务响应异常: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"❌ {name} 服务连接失败: {e}")
        return False


async def check_database():
    """检查数据库连接"""
    try:
        import mysql.connector
        from almond_parser.config import settings
        
        conn = mysql.connector.connect(
            host=settings.MYSQL_HOST,
            port=settings.MYSQL_PORT,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            database=settings.MYSQL_DATABASE,
            connect_timeout=5
        )
        
        if conn.is_connected():
            logger.info("✅ MySQL 数据库连接正常")
            conn.close()
            return True
        else:
            logger.error("❌ MySQL 数据库连接失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ MySQL 数据库连接失败: {e}")
        return False


async def check_redis():
    """检查Redis连接"""
    try:
        import redis
        from almond_parser.config import settings
        
        r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD if settings.REDIS_PASSWORD else None,
            db=settings.REDIS_DB,
            socket_connect_timeout=5
        )
        
        r.ping()
        logger.info("✅ Redis 连接正常")
        return True
        
    except Exception as e:
        logger.error(f"❌ Redis 连接失败: {e}")
        return False


async def main():
    """主函数"""
    logger.info("🔍 开始检查系统状态...")
    
    # 检查基础服务
    services = [
        ("http://localhost:8010/health", "Almond Parser"),
        ("http://localhost:8000/health", "MinerU API"),
    ]
    
    service_results = []
    for url, name in services:
        result = await check_service(url, name)
        service_results.append(result)
    
    # 检查数据库
    db_ok = await check_database()
    redis_ok = await check_redis()
    
    # 汇总结果
    all_ok = all(service_results) and db_ok and redis_ok
    
    logger.info("\n📊 系统状态汇总:")
    logger.info(f"  - Almond Parser: {'✅' if service_results[0] else '❌'}")
    logger.info(f"  - MinerU API: {'✅' if service_results[1] else '❌'}")
    logger.info(f"  - MySQL: {'✅' if db_ok else '❌'}")
    logger.info(f"  - Redis: {'✅' if redis_ok else '❌'}")
    
    if all_ok:
        logger.info("🎉 所有服务状态正常")
    else:
        logger.warning("⚠️  部分服务存在问题，请检查配置和启动状态")
    
    return all_ok


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
