@echo off
echo 启动杏仁解析服务...

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    pause
    exit /b 1
)

REM 启动almond_parser服务
echo 正在启动 almond_parser 服务...
cd almond_parser
start "Almond Parser" cmd /k "python -m almond_parser.main"

REM 等待一下让服务启动
timeout /t 3 /nobreak >nul

REM 检查服务是否启动成功
echo 检查服务状态...
curl -s http://localhost:8010/health >nul 2>&1
if errorlevel 1 (
    echo 警告: almond_parser 服务可能未正常启动
) else (
    echo ✅ almond_parser 服务启动成功
)

echo.
echo 服务启动完成！
echo - almond_parser: http://localhost:8010
echo - API文档: http://localhost:8010/docs
echo.
echo 按任意键继续...
pause >nul
