# MineruAPI 后端优化方案

## 问题分析

### 原有问题
1. **重复检测浪费时间**：
   - 启动时已知平台类型和节点配置
   - 运行时每次请求还要重复检测sglang服务
   - Windows平台明知不支持sglang，还要尝试连接

2. **性能影响**：
   - 每次OCR请求都要检查sglang服务状态
   - 失败后才回退到pipeline模式
   - 增加了不必要的延迟

## 优化方案

### 1. 后端管理器 (BackendManager)
- **启动时确定**：在服务启动时一次性检测支持的后端
- **缓存配置**：将支持的后端列表缓存在内存中
- **快速选择**：运行时直接从缓存中选择最优后端

### 2. 配置驱动
- **backend-mode参数**：支持auto/pipeline-only/sglang-only三种模式
- **智能检测**：根据平台和配置自动确定支持的后端
- **强制模式**：允许用户强制指定使用特定后端

## 实现细节

### 新增文件
1. `mineru_api/services/backend_manager.py` - 后端管理器

### 修改文件
1. `mineru_api/start_server.py` - 启动脚本，集成后端管理器
2. `mineru_api/services/ocr_service.py` - 简化后端选择逻辑
3. `mineru_api/api/__init__.py` - 添加后端信息API

### 新增启动参数
```bash
# 自动检测模式（默认）
python start_server.py --backend-mode auto

# 仅使用pipeline模式
python start_server.py --backend-mode pipeline-only

# 仅使用sglang模式（需要Linux平台）
python start_server.py --backend-mode sglang-only
```

## 性能提升

### 启动时检测（一次性）
```
🔧 初始化后端管理器 (模式: auto)...
检查 sglang 服务支持...
✅ sglang 服务已运行
📋 支持的后端模式:
  ✅ pipeline: 传统模式
  ✅ vlm-sglang-client: 加速模式 (sglang服务: http://127.0.0.1:30000)
  ✅ vlm: 加速模式 (sglang服务: http://127.0.0.1:30000)
  ✅ sglang: 加速模式 (sglang服务: http://127.0.0.1:30000)
  ✅ vlm-transformers: 传统模式
```

### 运行时选择（快速）
```
使用后端: vlm-sglang-client (server: http://127.0.0.1:30000)
```

## API接口

### 获取后端信息
```http
GET /api/v1/backends
```

响应示例：
```json
{
  "backend_info": {
    "platform": "linux",
    "sglang_available": true,
    "sglang_url": "http://127.0.0.1:30000",
    "supported_backends": [
      "pipeline",
      "vlm-sglang-client",
      "vlm",
      "sglang",
      "vlm-transformers"
    ],
    "backend_mapping": {
      "pipeline": "pipeline",
      "vlm": "vlm-sglang-client",
      "sglang": "vlm-sglang-client",
      "vlm-sglang-client": "vlm-sglang-client",
      "vlm-transformers": "vlm-transformers"
    }
  },
  "message": "后端配置信息"
}
```

## 使用场景

### 1. Windows环境（pipeline-only）
```bash
# Windows自动检测，只支持pipeline
python start_server.py
# 或明确指定
python start_server.py --backend-mode pipeline-only
```

### 2. Linux环境（auto模式）
```bash
# 自动检测sglang服务，支持多种后端
python start_server.py --auto-sglang
```

### 3. 强制sglang模式
```bash
# 仅使用sglang，如果不可用则启动失败
python start_server.py --backend-mode sglang-only
```

## 优势总结

1. **性能提升**：
   - 消除运行时重复检测
   - 减少请求处理延迟
   - 避免不必要的网络连接

2. **配置清晰**：
   - 启动时明确显示支持的后端
   - 用户可以预知服务能力
   - 便于问题排查

3. **灵活控制**：
   - 支持多种启动模式
   - 可以强制使用特定后端
   - 适应不同部署环境

4. **向后兼容**：
   - 保持原有API接口不变
   - 默认行为与原来一致
   - 渐进式优化

## 测试建议

1. **Windows环境测试**：
   - 验证pipeline-only模式正常工作
   - 确认不会尝试启动sglang服务

2. **Linux环境测试**：
   - 测试auto模式的sglang检测
   - 验证sglang-only模式的强制要求

3. **性能测试**：
   - 对比优化前后的请求响应时间
   - 验证启动时间是否有显著改善

4. **API测试**：
   - 测试/backends接口返回正确信息
   - 验证不同模式下的后端选择逻辑
